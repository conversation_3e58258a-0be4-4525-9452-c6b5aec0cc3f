default:
  tags:
    - zqli-lab-p310-team

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  PACKAGE_VERSION: "$CI_COMMIT_TAG"
  APP_NAME: "pxl"
  APP_EXEC: "pxl"
  APP_BINARY_PREFIX: "$APP_NAME-$PACKAGE_VERSION"
  EXECUTABLE_NAME: "pxl.exe"
  PACKAGE_REGISTRY_URL: "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/pxl/${PACKAGE_VERSION}"
  PACKAGE_NAME: "pxl.zip"
  ARTIFACT_ROOT: "${APP_NAME}"
  WHEELS_PATH: "wheels"
  WHEELS_PKG: "pxl-${PACKAGE_VERSION}-py3-none-any.whl"

.before_script_template:
  before_script:
    # PowerShell
    - git config --global http.sslVerify false
    - $SHORT_COMMIT_SHA = $Env:CI_COMMIT_SHA.Substring(0,7)
    - $APP_BINARY = "${Env:APP_BINARY_PREFIX}-win-amd64-$SHORT_COMMIT_SHA.exe"
    - $Env:APP_BINARY = $APP_BINARY

stages:
  - lint
  - test
  - build
  - upload
  - release

lint-job:
  stage: lint
  script:
    - py -3 --version  # For debugging
    - python -m pip install --upgrade pip
    - py -3 -m venv lint_env
    - lint_env\Scripts\activate
    - pip install flake8 mypy
    - flake8 src test
    - mypy src

unit-test-job:
  extends: .before_script_template
  stage: test
  script:
    - py -3 --version  # For debugging
    - py -3 -m venv unit_test_env
    - unit_test_env\Scripts\activate
    - pip install -e .[test]
    - $env:PYTHONPATH="src"
    - coverage run --append -m unittest test.unit_test.test_cie1937
    - coverage run --append -m unittest test.unit_test.test_de2k
    - coverage run --append -m unittest test.unit_test.test_de_calc.TestDeltaECalculatorPlot.test_static_mode
    - coverage run --append -m unittest test.unit_test.test_de_calc.TestDeltaECalculator
    - coverage run --append -m unittest test.unit_test.test_de_calc_websrv
    - coverage run --append -m unittest test.unit_test.test_gdmb_calc.TestGDMBCalculator
    - coverage run --append -m unittest test.unit_test.test_gdmb_calc.TestGDMBComparison
    - coverage run --append -m unittest test.unit_test.test_gdmb_calc_websrv
    - coverage report
    - coverage html
  artifacts:
    when: always
    paths:
      - htmlcov/
    expire_in: 1 week

build-wheels:
  stage: build
  needs: [unit-test-job]
  script:
    - py -3 --version  # For debugging
    - py -3 -m venv build_de_env
    - build_de_env\Scripts\activate
    - pip install -e .[build]
    - python build_script.py $WHEELS_PATH
  artifacts:
    when: always
    paths:
      - $WHEELS_PATH/*.whl
    expire_in: 1 week

upload-job:
  extends: .before_script_template
  stage: upload
  rules:
    - if: $CI_COMMIT_TAG                 # Run this job when a tag is created
  script:
    - echo $PACKAGE_VERSION
    - echo "Uploading to $PACKAGE_REGISTRY_URL/$WHEELS_PKG"
    - 'curl.exe -v -f --header "JOB-TOKEN: $CI_JOB_TOKEN" --upload-file $WHEELS_PATH/$WHEELS_PKG "$PACKAGE_REGISTRY_URL/$WHEELS_PKG"'

  dependencies:
  - build-wheels

release-job:
  extends: .before_script_template
  stage: release
  rules:
    - if: $CI_COMMIT_TAG
  variables:
    NAME: "Release $CI_COMMIT_TAG"
    DATA_PACKET: "{ \"name\": \"Release $CI_COMMIT_TAG\", \"tag_name\": \"$CI_COMMIT_TAG\", \"assets\": { \"links\": [ 
      { \"name\": \"$WHEELS_PKG\", \"url\": \"$PACKAGE_REGISTRY_URL/$WHEELS_PKG\", \"direct_asset_path\": \"/$WHEELS_PKG\", \"link_type\": \"package\" }] } }"
  script:
    - '$DATA_PACKET.trim(''"'') | ConvertTo-Json -outvariable WIN_DATA'
    - '$FORM_DATA = $WIN_DATA.trim(''"'')'
    - 'curl.exe -v --fail-with-body --request POST "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases"  --header "Content-Type: application/json" --header "JOB-TOKEN: $CI_JOB_TOKEN" --data $FORM_DATA'
