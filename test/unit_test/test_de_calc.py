#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: June 17, 2024
"""

import inspect
import unittest
from pathlib import Path

import numpy as np
from parameterized import parameterized

from pxl.de_calc.de_calc import DeltaECalculator
from pxl.dlb.watermarks import WATERMARK_DLB_BASE64
from pxl.util.error_handling import log_and_raise
from pxl.util.exceptions import FileOperationError, CalculationError
from pxl.util.logging_config import get_logger
from pxl.util.de_sheet import DeltaE2kCalculatorExcelSheet


def _gen_xyz_data(index: int):
    return [
        {  # 0: FFALCON_Unit1_HDMI_Sink-led_ext_dark_Extend_DM4_ExtendedColors_DeltaE2k_Calculator_v2.3 [0:36]
            'Tmax': 338.0,
            'measures': (
                np.array([
                    [321.081575, 337.818478, 367.903832], [19.570348, 18.321265, 16.761252],
                    [67.025945, 55.2077, 26.181701],
                    [68.173417, 56.384438, 33.539213], [26.481546, 20.333336, 10.274569],
                    [11.419231, 7.798708, 1.61401],
                    [18.613446, 15.441287, 8.970713], [1.538453, 1.124622, 0.366432],
                    [21.086612, 16.108895, 5.428451],
                    [39.976063, 25.092505, 16.357796], [43.172697, 28.960129, 15.842219],
                    [13.74971, 14.570202, 19.929721],
                    [9.241436, 9.601681, 11.750731], [2.026306, 2.316711, 2.805219],
                    [12.918113, 10.030092, 51.386015],
                    [52.061543, 43.60189, 22.167118], [95.53188, 108.110577, 245.053255],
                    [5.074443, 4.454609, 19.681527],
                    [71.048908, 76.118226, 203.339585], [37.708264, 51.079045, 105.571459],
                    [37.683085, 27.28449, 6.038621],
                    [54.436993, 76.15391, 114.58929], [44.172341, 21.29622, 1.382521],
                    [12.588712, 7.5713, 2.326633],
                    [89.032436, 84.916958, 10.963751], [150.17729, 152.546469, 79.007773],
                    [21.352988, 15.19447, 91.354133],
                    [51.564589, 24.813842, 2.418366], [188.844158, 168.896806, 17.725854],
                    [7.244425, 8.821279, 1.087238],
                    [19.01108, 18.753834, 4.549979], [6.902525, 10.043279, 3.26872],
                    [13.57125, 13.209671, 7.986952],
                    [4.762198, 3.860321, 0.770617], [24.542593, 27.228522, 50.149577],
                    [38.049299, 27.477243, 9.464653],
                    [40.456902, 35.13307, 14.607358]]),
                np.array([
                    [321.393, 336.042, 367.659], [22.0407, 20.8312, 19.4579], [72.1371, 60.5742, 30.1863],
                    [73.7628, 62.3476, 38.299], [29.6921, 23.1438, 12.5244], [13.0183, 9.06341, 2.41788],
                    [21.1031, 17.7224, 10.9264], [1.68436, 1.21436, 0.622943], [23.5371, 18.4163, 6.83537],
                    [43.4601, 27.9815, 19.3289], [46.76, 32.1637, 18.439], [15.7717, 16.8911, 22.6334],
                    [10.8643, 11.2573, 13.7606], [2.36891, 2.78099, 3.24013], [13.3388, 10.7501, 52.7403],
                    [56.1148, 47.9302, 25.996], [99.7211, 113.992, 248.461], [5.496, 4.98842, 20.7233],
                    [74.2368, 81.1973, 207.132], [38.0779, 53.0957, 103.543], [40.7845, 30.3476, 7.66323],
                    [58.1865, 81.269, 118.722], [46.6493, 22.7356, 2.17326], [14.5336, 8.95668, 3.30093],
                    [94.2844, 91.5758, 14.6296], [155.553, 158.037, 85.6518], [22.0478, 16.4758, 92.1042],
                    [54.3235, 26.3986, 3.1946], [192.573, 173.083, 20.5257], [8.46937, 10.1638, 1.88734],
                    [21.7236, 21.5937, 6.22258], [8.22959, 11.8248, 4.44227], [15.4955, 15.1936, 9.70209],
                    [5.50292, 4.52918, 1.26144], [26.9776, 30.0747, 53.6583], [41.481, 30.7355, 11.8936],
                    [44.0357, 38.9054, 17.5708]])
            ),
        },
        {  # 1: LG_C2_WCG_DR_extended_DE2K_1nit [36:1]
            'Tmax': 809.0,
            'measures': (
                np.array([
                    [0.615450, 0.647532, 0.705199], [0.557853, 0.586932, 0.639202], [0.495793, 0.521637, 0.568093],
                    [0.437711, 0.460527, 0.501541], [0.370630, 0.389950, 0.424678], [0.309601, 0.325739, 0.354749],
                    [0.249339, 0.262336, 0.285699], [0.187211, 0.196969, 0.214511], [0.127443, 0.134086, 0.146028],
                    [0.120417, 0.126694, 0.137977], [0.116986, 0.123085, 0.134046], [0.107020, 0.112598, 0.122626],
                    [0.100645, 0.105892, 0.115322], [0.097539, 0.102623, 0.111763], [0.091487, 0.096256, 0.104828],
                    [0.082807, 0.087124, 0.094883], [0.077285, 0.081313, 0.088555], [0.071972, 0.075724, 0.082468],
                    [0.064393, 0.067750, 0.073784], [0.059599, 0.062705, 0.068290], [0.055009, 0.057876, 0.063031],
                    [0.046438, 0.048858, 0.053210], [0.040535, 0.042648, 0.046446], [0.033355, 0.035094, 0.038219],
                    [0.028480, 0.029965, 0.032633], [0.021307, 0.022418, 0.024415], [0.014231, 0.014972, 0.016306],
                    [0.007285, 0.007665, 0.008347], [0.007285, 0.007665, 0.008347], [0.006609, 0.006954, 0.007573],
                    [0.005975, 0.006286, 0.006846], [0.004314, 0.004539, 0.004944], [0.003841, 0.004041, 0.004401],
                    [0.001399, 0.001471, 0.001603], [0.000950, 0.001000, 0.001089],
                    [0.000950, 0.001000, 0.001089]]),
                np.array([
                    [0.302500, 0.316500, 0.312200], [0.291800, 0.303700, 0.300000], [0.251900, 0.262100, 0.256200],
                    [0.220500, 0.227000, 0.224100], [0.195300, 0.201000, 0.203900], [0.153500, 0.159100, 0.164000],
                    [0.124600, 0.131100, 0.135100], [0.092000, 0.098600, 0.100700], [0.062300, 0.066100, 0.067100],
                    [0.060900, 0.065000, 0.066700], [0.058100, 0.062400, 0.062300], [0.055000, 0.059000, 0.058200],
                    [0.053500, 0.057300, 0.056000], [0.052800, 0.056700, 0.055900], [0.047100, 0.051500, 0.047800],
                    [0.043000, 0.047300, 0.042500], [0.041200, 0.045400, 0.043000], [0.037100, 0.040300, 0.034700],
                    [0.034000, 0.037000, 0.033600], [0.031500, 0.032700, 0.028400], [0.029300, 0.029700, 0.026000],
                    [0.026200, 0.025800, 0.021900], [0.018300, 0.016000, 0.013200], [0.014200, 0.010400, 0.009200],
                    [0.010300, 0.006600, 0.003400], [0.009700, 0.006300, 0.003600], [0.008000, 0.005100, 0.002400],
                    [0.003600, 0.001800, 0.000000], [0.003500, 0.001900, -0.000100], [0.003400, 0.001900, 0.000800],
                    [0.002800, 0.001600, 0.000300], [0.001300, 0.000600, -0.001300],
                    [0.000600, 0.000300, -0.002200],
                    [-0.000500, -0.000500, -0.001300], [-0.000500, -0.000300, -0.001000],
                    [-0.000400, -0.000400, -0.001100]])
            ),
        },
        {  # 2: LG_C2_WCG_DR_extended_DE2K_1nit [45:37]
            'Tmax': 809.0,
            'measures': (
                np.array([[769.455779, 809.564923, 881.662954], [769.455779, 809.564923, 881.662954],
                          [768.629055, 808.695104, 880.715671], [333.061228, 350.422590, 381.630438],
                          [206.187064, 216.934903, 236.254637], [57.480647, 60.476920, 65.862858],
                          [30.864333, 32.473187, 35.365176], [6.214817, 6.538774, 7.121103],
                          [3.082968, 3.243673, 3.532547]]),
                np.array([[676.830900, 712.629100, 769.409600], [671.412300, 706.715700, 762.451500],
                          [665.430900, 700.210800, 755.972500], [288.956300, 299.804300, 322.250000],
                          [182.535300, 189.403400, 202.542100], [54.797800, 56.681400, 61.551300],
                          [29.598400, 30.667000, 33.377600], [6.020600, 6.300300, 6.722100],
                          [3.054500, 3.153000, 3.305600]])
            ),
        },
        {  # 3: LG_C2_WCG_DR_extended_DE2K_1nit [45:57]
            'Tmax': 809.0,
            'measures': (
                np.array([[769.455779, 809.564923, 881.662954], [78.371779, 36.793755, 0.978646],
                          [42.392271, 111.882819, 8.583581], [32.621656, 13.886110, 171.210678],
                          [71.858564, 35.115593, 0.601443], [42.247619, 107.816806, 8.211475],
                          [32.452962, 14.193741, 168.603467], [67.912038, 35.822413, 3.550069],
                          [56.960718, 113.337424, 18.766981], [32.452962, 14.193741, 168.603467],
                          [65.824066, 38.222451, 11.312017], [63.496910, 114.067851, 31.514607],
                          [22.346434, 14.342105, 85.033967]]),
                np.array([[676.830900, 712.629100, 769.409600], [51.187500, 24.161800, 0.244700],
                          [27.817600, 75.722200, 5.961400], [24.877600, 12.053000, 130.067900],
                          [48.354100, 24.427600, 0.275900], [31.758400, 75.700800, 5.794500],
                          [24.296900, 12.241500, 125.968100], [49.280000, 27.016600, 2.643800],
                          [46.102500, 88.044200, 14.940700], [24.409500, 12.301500, 126.477600],
                          [51.155800, 31.206700, 9.242800], [54.381900, 94.063800, 26.590300],
                          [22.232800, 15.268300, 84.000800]])
            ),
        },
        {  # 4: DeltaE2k_Calculator_v2.3_709082_Sony_55XR70_HDMI_SinkLed_DM4_FHD_AvgAPL_SDKv5.2 [0:56]
            'Tmax': 1320.0,
            'measures': (
                np.array([[1254.601778, 1319.999952, 1437.556178], [3.363515, 3.538844, 3.854005],
                          [16.462967, 17.321127, 18.863707], [41.450655, 43.611338, 47.495266],
                          [77.444892, 81.515959, 88.493467], [123.973932, 130.436277, 142.052638],
                          [179.410001, 188.816017, 205.185316], [243.336588, 256.020907, 278.821553],
                          [312.382664, 328.739172, 357.412025], [33.401645, 17.308142, 1.533894],
                          [94.822545, 57.121447, 20.840471], [147.049040, 76.322307, 6.820275],
                          [197.493172, 140.079476, 86.948429], [29.853718, 59.629614, 9.833498],
                          [79.357891, 145.233211, 37.702749], [124.435924, 248.280219, 40.871928],
                          [164.352944, 267.299602, 106.663291], [14.939663, 6.474874, 77.898794],
                          [52.246460, 31.761963, 209.054011], [73.619512, 31.875320, 383.681545],
                          [131.658789, 101.613248, 387.060764], [62.749053, 75.777705, 11.152137],
                          [149.125208, 177.709267, 39.609099], [254.226261, 306.835799, 45.035569],
                          [267.650446, 313.451475, 107.190711], [50.510496, 24.439346, 82.048745],
                          [129.966231, 71.319336, 204.501603], [226.350932, 109.569043, 368.367344],
                          [247.617155, 159.227938, 369.323562], [44.843549, 65.589716, 88.631333],
                          [111.264186, 156.922291, 208.700125], [185.466616, 271.234846, 366.667573],
                          [211.162732, 282.914383, 365.408774], [41.980257, 37.355245, 23.374229],
                          [139.191403, 128.840302, 90.057380], [66.841177, 71.652552, 135.937230],
                          [39.297865, 50.306645, 23.942193], [94.024268, 88.626387, 170.929992],
                          [112.324885, 157.377309, 164.621549], [140.112338, 109.337108, 20.083611],
                          [52.068835, 44.420028, 157.377619], [107.677596, 70.693981, 47.839464],
                          [30.851341, 22.428264, 53.186484], [121.510090, 161.489056, 37.240791],
                          [166.683632, 153.439626, 25.158370], [29.933257, 21.912257, 114.538925],
                          [53.956477, 88.957177, 34.161102], [75.689073, 43.720238, 15.908946],
                          [197.324517, 208.920582, 30.564317], [115.043509, 73.014192, 119.362206],
                          [61.389763, 79.381887, 153.901752], [286.967694, 303.745119, 317.501611],
                          [196.966186, 208.268863, 225.322790], [126.392975, 133.598836, 145.382142],
                          [68.090536, 72.048476, 78.646823], [30.737746, 32.735796, 35.967822],
                          [9.308461, 9.750411, 10.976473]]),
                np.array([[1399.200000, 1512.580000, 1514.780000], [3.460000, 3.650000, 3.980000],
                          [15.120000, 15.940000, 17.760000], [38.450000, 40.490000, 44.550000],
                          [71.950000, 76.550000, 80.390000], [116.940000, 125.060000, 130.420000],
                          [173.550000, 185.400000, 188.510000], [238.010000, 254.950000, 256.790000],
                          [315.230000, 335.520000, 340.840000], [33.060000, 16.800000, 2.220000],
                          [95.650000, 56.860000, 18.600000], [159.830000, 82.190000, 8.140000],
                          [201.070000, 142.280000, 77.290000], [28.210000, 59.110000, 10.070000],
                          [75.420000, 145.260000, 34.180000], [124.640000, 258.070000, 39.260000],
                          [159.850000, 272.120000, 94.650000], [15.160000, 5.700000, 76.150000],
                          [50.610000, 29.320000, 204.200000], [75.760000, 29.760000, 380.720000],
                          [127.040000, 97.930000, 365.140000], [60.150000, 74.060000, 10.790000],
                          [147.320000, 178.440000, 36.980000], [258.250000, 315.500000, 42.250000],
                          [270.500000, 321.660000, 93.980000], [47.690000, 22.260000, 76.570000],
                          [127.510000, 68.610000, 193.090000], [228.730000, 108.400000, 346.990000],
                          [247.220000, 159.130000, 347.700000], [41.160000, 62.660000, 82.430000],
                          [106.540000, 155.870000, 197.240000], [179.140000, 275.380000, 348.300000],
                          [204.760000, 286.470000, 342.900000], [38.350000, 34.520000, 20.210000],
                          [134.460000, 126.830000, 79.870000], [62.890000, 68.860000, 127.480000],
                          [36.220000, 47.970000, 19.980000], [89.250000, 85.230000, 155.950000],
                          [107.230000, 154.060000, 152.340000], [142.630000, 110.390000, 17.410000],
                          [48.870000, 42.130000, 147.480000], [105.680000, 68.510000, 41.900000],
                          [28.260000, 20.350000, 49.700000], [119.660000, 162.640000, 33.750000],
                          [168.400000, 155.460000, 22.010000], [27.560000, 19.580000, 106.510000],
                          [49.800000, 85.900000, 29.600000], [75.220000, 43.150000, 14.210000],
                          [199.760000, 212.680000, 28.290000], [111.710000, 70.430000, 108.550000],
                          [56.860000, 75.970000, 142.000000], [285.030000, 306.000000, 296.780000],
                          [190.100000, 205.940000, 206.780000], [118.710000, 128.410000, 134.180000],
                          [63.030000, 67.850000, 72.250000], [27.520000, 29.760000, 32.950000],
                          [8.040000, 8.560000, 10.080000]])
            ),
        },

    ][index]


class TestDeltaECalculatorPlot(unittest.TestCase):

    def test_dynamic_mode_local(self):
        print(f"{inspect.currentframe().f_code.co_name}")
        import threading

        saved_figure = (Path(__file__).parent / "sample_de_fig_test_dynamic_mode.png").resolve()

        data = _gen_xyz_data(4)

        tmax = data['Tmax']
        data_xyz_t, data_xyz_m = data['measures']

        def external_data_thread(calculator, interval):
            print("External data thread started")
            for i in range(0, len(data_xyz_t)):
                # processing
                target_xyz = data_xyz_t[i]
                measured_xyz = data_xyz_m[i]
                calculator.receive_data(i, target_xyz, measured_xyz, interval=interval)

            # close the plotting window at completion
            calculator.done()
            print("External data thread finished")

        try:
            de_cal = DeltaECalculator(
                target_max=tmax,
                max_de_threshold=(2.5, 10.0, float('inf')),
                scale_target=DeltaECalculator.EnumScaleTarget.AUTO,
                logo=WATERMARK_DLB_BASE64
            )
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to create DeltaECalculator: {str(e)}", exc_info=True)
            raise
        else:
            thread = threading.Thread(target=external_data_thread, args=(de_cal, 1.0,))
            thread.daemon = True  # it ends with the main thread
            thread.start()

            # plot and save figure upon completion
            de_cal.plot_figure(save_figure=saved_figure)
        self.assertTrue(Path(saved_figure).is_file())

    def test_static_mode(self):
        print(f"{inspect.currentframe().f_code.co_name}")
        saved_figure = (Path(__file__).parent / "sample_de_fig_test_static_mode.png").resolve()

        data = _gen_xyz_data(4)

        tmax = data['Tmax']
        data_xyz_t, data_xyz_m = data['measures']

        try:
            de_cal = DeltaECalculator(
                dynamic=False,
                target_max=tmax,
                max_de_threshold=(2.5, 10.0, float('inf')),
                scale_target=DeltaECalculator.EnumScaleTarget.AUTO,
                show_figure=False,
                logo=WATERMARK_DLB_BASE64
            )
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to create DeltaECalculator: {str(e)}", exc_info=True)
            raise
        else:
            de_cal.batch_receive_data(data_xyz_t, data_xyz_m)

            # plot and save figure upon completion
            de_cal.plot_figure(save_figure=saved_figure)
        self.assertTrue(Path(saved_figure).is_file())


class TestDeltaECalculator(unittest.TestCase):

    @parameterized.expand([
        (
            "Sony_55XR70",
            (
                Path(__file__).parent /
                "../test_sheets/"
                "DeltaE2k_Calculator_v2.3_709082_Sony_55XR70_HDMI_SinkLed_DM4_FHD_AvgAPL_SDKv5.2.xlsx"
            ).resolve()
        ),
        (
            "Ffalcon_Unit1",
            (
                Path(__file__).parent /
                "../test_sheets/"
                "FFALCON_Unit1_HDMI_Sink-led_ext_dark_Extend_DM4_ExtendedColors_DeltaE2k_Calculator_v2.3.xlsx"
            ).resolve()
        ),
    ])
    def test_de_calc(self, name, de_sheet):
        print(f"{inspect.currentframe().f_code.co_name}::{name}")
        try:
            sheet = DeltaE2kCalculatorExcelSheet(de_sheet)
            measurement_xyz = sheet.get_measurement_xyz()
            expected_xyz = sheet.get_expected_xyz()
            de_ref = sheet.get_delta_e2000()
            scale_factor = sheet.get_scale_factor()
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to load Excel sheet: {str(e)}", exc_info=True)
            raise
        else:
            try:
                de_cal = DeltaECalculator(
                    dynamic=False,
                    max_de_threshold=(2.5, 10.0, float('inf')),
                    scale_target=DeltaECalculator.EnumScaleTarget.YES,
                    show_figure=False,
                    logo=WATERMARK_DLB_BASE64
                )
            except Exception as e:
                logger = get_logger(__name__)
                logger.error(f"Failed to create DeltaECalculator: {str(e)}", exc_info=True)
                raise
            else:
                de_cal.batch_receive_data(expected_xyz * scale_factor, measurement_xyz)

                # plot and save figure upon completion
                de_cal.plot_figure(save_figure=None)

                de_dut = de_cal._de_store
                print(f"de_dut: {de_dut}\n de_tgt: {de_ref}")

                # Select the shorter list
                min_length = min(len(de_dut), len(de_ref))
                A_short = de_dut[:min_length]
                B_short = de_ref[:min_length]

                # Comparison
                comparison = np.abs(np.array(A_short) - np.array(B_short)) < 1e-2
                self.assertTrue(np.all(comparison), f"Arrays differ: {A_short} vs {B_short}")

                # Overall result
                de_report = de_cal.get_report()
                self.assertGreater(
                    de_report.max_de, 0.0,
                    msg=f"Maximum Delta E should be positive. Actual = {de_report.max_de}"
                )
                self.assertGreater(
                    de_report.avg_de, 0.0,
                    msg=f"Average Delta E should be positive. Actual = {de_report.avg_de}"
                )


class TestDeltaECalculatorPlotManual(unittest.TestCase):
    def test_static_mode(self):
        print(f"{inspect.currentframe().f_code.co_name}")

        # tmax = 809.0
        # de_sheet = (Path(__file__).parent /
        #             "../test_sheets/"
        #             "LG_C2_WCG_DR_extended_DE2K_1nit.xlsx").resolve()
        # index_ref_white = 35  # 1 nit

        tmax = 539.0
        de_sheet = (
            Path(__file__).parent /
            "../test_sheets/"
            "TCL_55Q650F_WCG_DR_extended_DE2K_1nit.xlsx"
        ).resolve()

        index_ref_white = 35  # 1 nit

        try:
            sheet = DeltaE2kCalculatorExcelSheet(de_sheet)
            measurement_xyz = sheet.get_measurement_xyz()
            expected_xyz = sheet.get_expected_xyz()
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to load Excel sheet: {str(e)}", exc_info=True)
            raise
        else:
            try:
                de_cal = DeltaECalculator(
                    dynamic=False,
                    target_max=tmax,
                    max_de_threshold=(2.5, 10.0, float('inf')),
                    scale_target=DeltaECalculator.EnumScaleTarget.YES,
                    logo=WATERMARK_DLB_BASE64
                )
            except Exception as e:
                logger = get_logger(__name__)
                logger.error(f"Failed to create DeltaECalculator: {str(e)}", exc_info=True)
                raise
            else:
                de_cal.set_reference_white(expected_xyz[index_ref_white], measurement_xyz[index_ref_white])
                de_cal.batch_receive_data(expected_xyz, measurement_xyz)

                # plot and save figure upon completion
                de_cal.plot_figure()


if __name__ == '__main__':
    unittest.main()
