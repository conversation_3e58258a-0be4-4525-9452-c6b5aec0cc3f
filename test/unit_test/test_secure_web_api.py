#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import unittest
import json
import threading
import time
from unittest.mock import patch
import numpy as np

from pxl.web.api import create_secure_app, create_development_app
from pxl.web.security import SecurityConfig
from pxl.web.auth import AuthManager, APIKeyAuth
from pxl.util.logging_config import get_logger


class TestSecureWebAPI(unittest.TestCase):
    """Test cases for the secure web API."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
        
        # Create test API keys
        self.test_api_keys = {
            'test-key-123': {
                'user_id': 'test_user',
                'permissions': {'*:*'}  # Full access for testing
            },
            'limited-key-456': {
                'user_id': 'limited_user',
                'permissions': {'de_calc:calculate'}  # Limited access
            }
        }
        
        # Create auth manager
        api_key_auth = APIKeyAuth(self.test_api_keys)
        self.auth_manager = AuthManager(api_key_auth)
        
        # Create security config
        self.security_config = SecurityConfig(
            require_api_key=True,
            rate_limit_requests=10,
            rate_limit_window=60,
            cors_origins={'http://localhost:3000'},
            enable_security_headers=True
        )
        
        # Create secure app
        self.app = create_secure_app(self.security_config, self.auth_manager)
        self.client = self.app.test_client()
        
        # Create development app for comparison
        self.dev_app = create_development_app()
        self.dev_client = self.dev_app.test_client()
    
    def test_health_check_no_auth_required(self):
        """Test that health check doesn't require authentication."""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
        self.assertEqual(data['service'], 'PXL API')
    
    def test_api_info_no_auth_required(self):
        """Test that API info doesn't require authentication."""
        response = self.client.get('/api/info')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['name'], 'PXL API')
        self.assertIn('endpoints', data)
        self.assertIn('authentication', data)
    
    def test_delta_e_calculation_requires_auth(self):
        """Test that Delta E calculation requires authentication."""
        payload = {
            'target_xyz': [0.3, 0.3, 0.3],
            'measured_xyz': [0.31, 0.29, 0.32],
            'max_de_threshold': [2.5, 10.0, float('inf')]
        }
        
        # Request without API key should fail
        response = self.client.post('/api/de/calculate', 
                                   json=payload,
                                   content_type='application/json')
        self.assertEqual(response.status_code, 401)
    
    def test_delta_e_calculation_with_valid_auth(self):
        """Test Delta E calculation with valid authentication."""
        payload = {
            'target_xyz': [0.3, 0.3, 0.3],
            'measured_xyz': [0.31, 0.29, 0.32],
            'max_de_threshold': [2.5, 10.0, float('inf')],
            'scale_target': 'auto'
        }
        
        headers = {'X-API-Key': 'test-key-123'}
        
        response = self.client.post('/api/de/calculate',
                                   json=payload,
                                   headers=headers,
                                   content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertIn('delta_e', data)
        self.assertIn('target_xyz', data)
        self.assertIn('measured_xyz', data)
    
    def test_delta_e_calculation_with_limited_auth(self):
        """Test Delta E calculation with limited permissions."""
        payload = {
            'target_xyz': [0.3, 0.3, 0.3],
            'measured_xyz': [0.31, 0.29, 0.32],
            'max_de_threshold': [2.5, 10.0, float('inf')]
        }
        
        headers = {'X-API-Key': 'limited-key-456'}
        
        response = self.client.post('/api/de/calculate',
                                   json=payload,
                                   headers=headers,
                                   content_type='application/json')
        
        self.assertEqual(response.status_code, 200)  # Should work with de_calc:calculate permission
    
    def test_gdmb_calculation_with_limited_auth(self):
        """Test GDMB calculation with limited permissions (should fail)."""
        payload = {
            'target_y': 0.3,
            'measured_y': 0.31,
            'tmax': 500.0
        }
        
        headers = {'X-API-Key': 'limited-key-456'}
        
        response = self.client.post('/api/gdmb/calculate',
                                   json=payload,
                                   headers=headers,
                                   content_type='application/json')
        
        self.assertEqual(response.status_code, 403)  # Should fail due to insufficient permissions
    
    def test_input_validation(self):
        """Test input validation."""
        headers = {'X-API-Key': 'test-key-123'}
        
        # Test missing required fields
        response = self.client.post('/api/de/calculate',
                                   json={},
                                   headers=headers,
                                   content_type='application/json')
        self.assertEqual(response.status_code, 400)
        
        # Test invalid XYZ values
        payload = {
            'target_xyz': [0.3, 0.3],  # Should have 3 elements
            'measured_xyz': [0.31, 0.29, 0.32],
            'max_de_threshold': [2.5, 10.0, float('inf')]
        }
        
        response = self.client.post('/api/de/calculate',
                                   json=payload,
                                   headers=headers,
                                   content_type='application/json')
        self.assertEqual(response.status_code, 400)
        
        # Test invalid threshold values
        payload = {
            'target_xyz': [0.3, 0.3, 0.3],
            'measured_xyz': [0.31, 0.29, 0.32],
            'max_de_threshold': [2.5, 10.0]  # Should have 3 elements
        }
        
        response = self.client.post('/api/de/calculate',
                                   json=payload,
                                   headers=headers,
                                   content_type='application/json')
        self.assertEqual(response.status_code, 400)
    
    def test_security_headers(self):
        """Test that security headers are set."""
        response = self.client.get('/health')
        
        # Check for security headers
        self.assertIn('X-Frame-Options', response.headers)
        self.assertIn('X-Content-Type-Options', response.headers)
        self.assertIn('X-XSS-Protection', response.headers)
        self.assertIn('Content-Security-Policy', response.headers)
        
        self.assertEqual(response.headers['X-Frame-Options'], 'DENY')
        self.assertEqual(response.headers['X-Content-Type-Options'], 'nosniff')
    
    def test_cors_headers(self):
        """Test CORS headers."""
        headers = {'Origin': 'http://localhost:3000'}
        response = self.client.get('/health', headers=headers)
        
        self.assertIn('Access-Control-Allow-Origin', response.headers)
        self.assertEqual(response.headers['Access-Control-Allow-Origin'], 'http://localhost:3000')
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        headers = {'X-API-Key': 'test-key-123'}
        payload = {
            'target_xyz': [0.3, 0.3, 0.3],
            'measured_xyz': [0.31, 0.29, 0.32],
            'max_de_threshold': [2.5, 10.0, float('inf')]
        }
        
        # Make requests up to the limit
        for i in range(10):  # Rate limit is 10 requests per window
            response = self.client.post('/api/de/calculate',
                                       json=payload,
                                       headers=headers,
                                       content_type='application/json')
            if i < 9:  # First 9 should succeed
                self.assertEqual(response.status_code, 200)
        
        # The 11th request should be rate limited
        response = self.client.post('/api/de/calculate',
                                   json=payload,
                                   headers=headers,
                                   content_type='application/json')
        self.assertEqual(response.status_code, 429)
    
    def test_development_app_no_auth(self):
        """Test that development app doesn't require authentication."""
        payload = {
            'target_xyz': [0.3, 0.3, 0.3],
            'measured_xyz': [0.31, 0.29, 0.32],
            'max_de_threshold': [2.5, 10.0, float('inf')]
        }
        
        # Development app should work without API key
        response = self.dev_client.post('/api/de/calculate',
                                       json=payload,
                                       content_type='application/json')
        
        # Should work in development mode
        self.assertIn(response.status_code, [200, 500])  # 500 might occur due to missing dependencies


if __name__ == '__main__':
    unittest.main()
