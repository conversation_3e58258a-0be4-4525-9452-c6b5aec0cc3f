#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: May 21, 2024
"""


import threading
import time
import unittest
import inspect

import matplotlib.pyplot as plt
import numpy as np
from parameterized import parameterized

from pxl.plot.cie1937 import CIE1931ChromaticityDiagram


class TestCIE1931ChromaticityDiagram(unittest.TestCase):
    test_sets = [
        (
            "test data 1",
            np.array([[1254.601778, 1319.999952, 1437.556178], [3.363515, 3.538844, 3.854005],
                      [312.382664, 328.739172, 357.412025], [33.401645, 17.308142, 1.533894],
                      [94.822545, 57.121447, 20.840471], [147.049040, 76.322307, 6.820275],
                      [197.493172, 140.079476, 86.948429], [29.853718, 59.629614, 9.833498],
                      [79.357891, 145.233211, 37.702749], [124.435924, 248.280219, 40.871928],
                      [164.352944, 267.299602, 106.663291], [14.939663, 6.474874, 77.898794],
                      [52.246460, 31.761963, 209.054011], [73.619512, 31.875320, 383.681545],
                      [131.658789, 101.613248, 387.060764], [62.749053, 75.777705, 11.152137],
                      [149.125208, 177.709267, 39.609099], [254.226261, 306.835799, 45.035569],
                      [267.650446, 313.451475, 107.190711], [50.510496, 24.439346, 82.048745],
                      [129.966231, 71.319336, 204.501603], [226.350932, 109.569043, 368.367344],
                      [247.617155, 159.227938, 369.323562], [44.843549, 65.589716, 88.631333],
                      [111.264186, 156.922291, 208.700125], [185.466616, 271.234846, 366.667573],
                      [211.162732, 282.914383, 365.408774], [41.980257, 37.355245, 23.374229],
                      [139.191403, 128.840302, 90.057380], [66.841177, 71.652552, 135.937230],
                      [39.297865, 50.306645, 23.942193], [94.024268, 88.626387, 170.929992],
                      [112.324885, 157.377309, 164.621549], [140.112338, 109.337108, 20.083611],
                      [52.068835, 44.420028, 157.377619], [107.677596, 70.693981, 47.839464],
                      [30.851341, 22.428264, 53.186484], [121.510090, 161.489056, 37.240791],
                      [166.683632, 153.439626, 25.158370], [29.933257, 21.912257, 114.538925],
                      [53.956477, 88.957177, 34.161102], [75.689073, 43.720238, 15.908946],
                      [197.324517, 208.920582, 30.564317], [115.043509, 73.014192, 119.362206],
                      [61.389763, 79.381887, 153.901752], [286.967694, 303.745119, 317.501611],
                      [196.966186, 208.268863, 225.322790], [126.392975, 133.598836, 145.382142],
                      [68.090536, 72.048476, 78.646823], [30.737746, 32.735796, 35.967822],
                      [9.308461, 9.750411, 10.976473]]),
            np.array([[1399.200000, 1512.580000, 1514.780000], [3.460000, 3.650000, 3.980000],
                      [315.230000, 335.520000, 340.840000], [33.060000, 16.800000, 2.220000],
                      [95.650000, 56.860000, 18.600000], [159.830000, 82.190000, 8.140000],
                      [201.070000, 142.280000, 77.290000], [28.210000, 59.110000, 10.070000],
                      [75.420000, 145.260000, 34.180000], [124.640000, 258.070000, 39.260000],
                      [159.850000, 272.120000, 94.650000], [15.160000, 5.700000, 76.150000],
                      [50.610000, 29.320000, 204.200000], [75.760000, 29.760000, 380.720000],
                      [127.040000, 97.930000, 365.140000], [60.150000, 74.060000, 10.790000],
                      [147.320000, 178.440000, 36.980000], [258.250000, 315.500000, 42.250000],
                      [270.500000, 321.660000, 93.980000], [47.690000, 22.260000, 76.570000],
                      [127.510000, 68.610000, 193.090000], [228.730000, 108.400000, 346.990000],
                      [247.220000, 159.130000, 347.700000], [41.160000, 62.660000, 82.430000],
                      [106.540000, 155.870000, 197.240000], [179.140000, 275.380000, 348.300000],
                      [204.760000, 286.470000, 342.900000], [38.350000, 34.520000, 20.210000],
                      [134.460000, 126.830000, 79.870000], [62.890000, 68.860000, 127.480000],
                      [36.220000, 47.970000, 19.980000], [89.250000, 85.230000, 155.950000],
                      [107.230000, 154.060000, 152.340000], [142.630000, 110.390000, 17.410000],
                      [48.870000, 42.130000, 147.480000], [105.680000, 68.510000, 41.900000],
                      [28.260000, 20.350000, 49.700000], [119.660000, 162.640000, 33.750000],
                      [168.400000, 155.460000, 22.010000], [27.560000, 19.580000, 106.510000],
                      [49.800000, 85.900000, 29.600000], [75.220000, 43.150000, 14.210000],
                      [199.760000, 212.680000, 28.290000], [111.710000, 70.430000, 108.550000],
                      [56.860000, 75.970000, 142.000000], [285.030000, 306.000000, 296.780000],
                      [190.100000, 205.940000, 206.780000], [118.710000, 128.410000, 134.180000],
                      [63.030000, 67.850000, 72.250000], [27.520000, 29.760000, 32.950000],
                      [8.040000, 8.560000, 10.080000]])
        ),
    ]

    @parameterized.expand(test_sets)
    def test_dynamic_mode(self, name, data_XYZ_t, data_XYZ_m):
        print(f"{inspect.currentframe().f_code.co_name}::{name}")
        # Create a figure with a single subplot for the chromaticity diagram
        fig, ax1 = plt.subplots(1, 1, figsize=(7 * 31 / 32, 7))

        # Initialize the chromaticity diagram with the subplot
        diagram = CIE1931ChromaticityDiagram(ax1)
        diagram.set_chroma_color_sets([
            ("Rec.709", ":", "green", [(0.64, 0.33), (0.30, 0.60), (0.15, 0.06)]),
            ("P3", ":", "purple", [(0.68, 0.32), (0.265, 0.69), (0.15, 0.06)]),
            ("Rec.2020", ":", "orange", [(0.708, 0.292), (0.170, 0.797), (0.131, 0.046)]),
        ])

        def update_color_data(diag, fig):
            for i, p in enumerate(data_XYZ_t):
                print(f"t = {data_XYZ_t[i]}; m = {data_XYZ_m[i]}")
                diag.add_a_color(data_XYZ_t[i])
                diag.add_b_color(data_XYZ_m[i])
                time.sleep(1)
            time.sleep(5)
            plt.close(fig)

        # simulate a thread that perform measurement
        thread = threading.Thread(target=update_color_data, args=(diagram, fig))
        thread.daemon = True  # it ends with the main thread
        thread.start()

        plt.show()
        self.assertEqual(True, True)  # add assertion here

    @parameterized.expand(test_sets)
    def test_static_mode(self, name, data_XYZ_t, data_XYZ_m):
        print(f"{inspect.currentframe().f_code.co_name}::{name}")
        # Create a figure with a single subplot for the chromaticity diagram
        fig, ax1 = plt.subplots(1, 1, figsize=(5 * 31 / 32, 5))

        # Initialize the chromaticity diagram with the subplot
        diagram = CIE1931ChromaticityDiagram(ax1)
        diagram.set_chroma_color_sets([
            ("Rec.709", ":", "green", [(0.64, 0.33), (0.30, 0.60), (0.15, 0.06)]),
            ("P3", ":", "purple", [(0.68, 0.32), (0.265, 0.69), (0.15, 0.06)]),
            ("Rec.2020", ":", "orange", [(0.708, 0.292), (0.170, 0.797), (0.131, 0.046)]),
        ])

        def update_color_data(diag, fig):
            for i, p in enumerate(data_XYZ_t):
                print(f"t = {data_XYZ_t[i]}; m = {data_XYZ_m[i]}")
                diag.add_a_color(data_XYZ_t[i], redraw=False)
                diag.add_b_color(data_XYZ_m[i], redraw=False)
            time.sleep(5)
            plt.close(fig)

        # simulate a thread that perform measurement
        thread = threading.Thread(target=update_color_data, args=(diagram, fig))
        thread.daemon = True  # it ends with the main thread
        thread.start()

        plt.show()
        self.assertEqual(True, True)  # add assertion here


if __name__ == '__main__':
    unittest.main()
