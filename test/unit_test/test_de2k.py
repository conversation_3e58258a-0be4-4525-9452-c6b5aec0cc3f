#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: May 29, 2024
"""


import inspect
import os
import unittest

import numpy as np
from parameterized import parameterized

from pxl.metrics.de2k import DE2000
from pxl.util.utils import raise_without_traceback
from pxl.util.de_sheet import DeltaE2kCalculatorExcelSheet


class TestDE2000(unittest.TestCase):

    @parameterized.expand([
        (
            np.array([319.39, 336.04, 365.97]),
            np.array([321.39, 336.04, 367.66]),
            np.array([321.08, 337.82, 367.90]),
            1.5344,
        ),
        (
            np.array([19.47, 18.22, 16.67]),
            np.array([22.04, 20.83, 19.46]),
            np.array([321.08, 337.82, 367.90]),
            1.5696,
        ),
        (
            np.array([44.172341, 21.29622, 1.382521]),
            np.array([46.6493, 22.7356, 2.17326]),
            np.array([321.08, 337.82, 367.90]),
            1.1786,
        ),
        (
            np.array([1437.6397706305675, 1512.5791, 1647.2859916578846]),
            np.array([1399.2023, 1512.5791, 1514.7751]),
            np.array([1437.63977073, 1512.5791, 1647.28599249]),
            7.2824,
        ),
        (
            np.array([164.352944, 267.299602, 106.663291]) * 1.1459,
            np.array([159.85277, 272.11978, 94.647057]),
            np.array([1437.63977073, 1512.5791, 1647.28599249]),
            2.7920,
        ),
    ])
    def test_de2k(self, XYZ1, XYZ2, WP, DE_TARGET):
        DE, _, __, ___ = DE2000(XYZ1, XYZ2, WP)
        print(f"DE = {DE}, DE_TARGET = {DE_TARGET}")
        self.assertTrue(abs(DE - DE_TARGET) < 5e-2)

    @parameterized.expand([
        ("Sony_55XR70",
         os.path.abspath(os.path.join(
             os.path.dirname(__file__),
             "../test_sheets/"
             "DeltaE2k_Calculator_v2.3_709082_Sony_55XR70_HDMI_SinkLed_DM4_FHD_AvgAPL_SDKv5.2.xlsx")
         )),
        ("Ffalcon_Unit1",
         os.path.abspath(os.path.join(
             os.path.dirname(__file__),
             "../test_sheets/"
             "FFALCON_Unit1_HDMI_Sink-led_ext_dark_Extend_DM4_ExtendedColors_DeltaE2k_Calculator_v2.3.xlsx")
         )),
        ("TCL_55Q650F_1nit",
         os.path.abspath(os.path.join(
             os.path.dirname(__file__),
             "../test_sheets/"
             "TCL_55Q650F_WCG_DR_extended_DE2K_1nit.xlsx")
         )),
        ("LG_C2_1nit",
         os.path.abspath(os.path.join(
             os.path.dirname(__file__),
             "../test_sheets/"
             "LG_C2_WCG_DR_extended_DE2K_1nit.xlsx")
         )),
    ])
    def test_de2k_with_sheet(self, name, de_sheet):
        print(f"{inspect.currentframe().f_code.co_name}::{name}")
        try:
            sheet = DeltaE2kCalculatorExcelSheet(de_sheet)
            measurement_xyz = sheet.get_measurement_xyz()
            expected_xyz = sheet.get_expected_xyz()
            de_ref = sheet.get_delta_e2000()
            ref_w = sheet.get_reference_white_xyz()
            scale_factor = sheet.get_scale_factor()
        except Exception as e:
            raise_without_traceback(type(e), e)
        else:
            # Select the shorter list
            min_length = min(len(measurement_xyz), len(expected_xyz), len(de_ref))

            for i, d in enumerate(de_ref[:min_length]):
                t, m = expected_xyz[i] * scale_factor, measurement_xyz[i]
                DE, DLP_W, DCP_W, DHP_W = DE2000(t, m, ref_w)
                print(f"t = {t}, m = {m}; DE vs. SHEET = {DE}, {d}; "
                      f"DLP_W = {DLP_W}, DCP_W = {DCP_W}, DHP_W = {DHP_W}")
                self.assertTrue(abs(DE - d) < 1e-2)


if __name__ == '__main__':
    unittest.main()
