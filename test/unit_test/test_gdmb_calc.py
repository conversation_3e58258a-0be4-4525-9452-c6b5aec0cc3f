#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: November 13, 2024
"""

import os
import unittest

import numpy as np
import pandas as pd
from parameterized import parameterized

from pxl.gdmb_calc.gdmb_calc import GDMBCalculator
from pxl.util.utils import raise_without_traceback
from pxl.dlb.watermarks import WATERMARK_DLB_BASE64
from pxl.util.gdmb_sheet import GDMBCalculatorExcelSheet


class TestGDMBComparison(unittest.TestCase):
    @parameterized.expand([
        (os.path.abspath(os.path.join(
            os.path.dirname(__file__),
            "../test_sheets/"
            "GDMB_Calculator_v1.1.xlsm")
        )),
    ])
    def test_gdmb_comparison(self, gdmb_sheet):
        try:
            sheet = GDMBCalculatorExcelSheet(gdmb_sheet)
            measurement_y_np = sheet.get_measurement_y()
            target_y_np = sheet.get_target_y()
            gdmb_ref = sheet.get_scaled_data()
        except Exception as e:
            raise_without_traceback(type(e), e)
        else:
            try:
                gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_y_np),
                                                           debug=True,
                                                           dynamic=False,
                                                           show_figure=False,
                                                           logo=WATERMARK_DLB_BASE64)
            except Exception as e:
                raise_without_traceback(type(e), e)
            else:
                gdmb_calc.batch_receive_data(target_y_list=target_y_np,
                                             measurement_y_list=measurement_y_np)
                # plot and save figure upon completion
                gdmb_calc.plot_figure(save_figure=None)
                gdmb_dut = gdmb_calc.scaled_data
                print(f"gdmb_dut: {gdmb_dut}\n gdmb_tgt: {gdmb_ref}")

                # Select the shorter list
                min_length = min(len(gdmb_dut), len(gdmb_ref))
                A_short = gdmb_dut[:min_length]
                B_short = gdmb_ref[:min_length]

                # Comparison
                comparison = np.abs(np.array(A_short) - np.array(B_short)) < 1e-2
                self.assertTrue(np.all(comparison), f"Arrays differ: {A_short} vs {B_short}")


class TestGDMBCalculator(unittest.TestCase):
    def test_dynamic_mode_positive(self):
        import threading  # Threading import call
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        def external_data_thread(calculator, interval):
            for i in range(measurement_np.size):
                target_y = target_np[i * 4]  # Target Step = 20
                measurement_y = measurement_np[i]  # Measurement Step = 80
                calculator.receive_data(i, target_y, measurement_y, redraw=True)
            calculator.done()
        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=False, dynamic=True,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            raise_without_traceback(type(e), e)
        else:
            try:
                thread = threading.Thread(target=external_data_thread, args=(gdmb_calc, 2.5,))
                thread.daemon = True
                thread.start()
                gdmb_calc.plot_figure()
                # Check that scaling is applied
                assert gdmb_calc.scale_target == "Y"
                # Check that scaled data is within target bounds
                lower_bound_bools = np.less(gdmb_calc.lower_tar, gdmb_calc.scaled_data)
                upper_bound_bools = np.greater(gdmb_calc.upper_tar, gdmb_calc.scaled_data)
                lb_set = set(lower_bound_bools)
                ub_set = set(upper_bound_bools)
                assert False not in lb_set and False not in ub_set
            except Exception:
                print("Test Dynamic Mode Positive Failed.")
                raise Exception("Test Dynamic Mode Positive Failed.")

    def test_negative_same_measurement(self):
        """
        Negative Test Case in which luminance data values remain constant. Validates that GDMB Test
        terminates measurement in response.
        """
        import threading  # Threading import call
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\negative_data_same", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\negative_data_same", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        def external_data_thread(calculator, interval):
            for i in range(measurement_np.size):
                target_y = target_np[i * 4]  # Target Step = 20
                measurement_y = measurement_np[i]  # Measurement Step = 80
                calculator.receive_data(i, target_y, measurement_y, redraw=True)
            calculator.done()

        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=False, dynamic=True,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            raise_without_traceback(type(e), e)
        else:
            try:
                thread = threading.Thread(target=external_data_thread, args=(gdmb_calc, 2.5,))
                thread.daemon = True
                thread.start()
                gdmb_calc.plot_figure()
                # Check that scaling is applied
                assert gdmb_calc.scale_target == "N"
            except Exception:
                print("Test Negative Same Measurement Failed.")
                raise Exception("Test Negative Same Measurement Failed.")

    def test_dynamic_mode_negative(self):
        import threading  # Threading import call
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\negative_data", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\negative_data", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        def external_data_thread(calculator, interval):
            for i in range(measurement_np.size):
                target_y = target_np[i * 4]  # Target Step = 20
                measurement_y = measurement_np[i]  # Measurement Step = 80
                calculator.receive_data(i, target_y, measurement_y, redraw=True)
            calculator.done()
        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=False, dynamic=True,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            raise_without_traceback(type(e), e)
        else:
            try:
                thread = threading.Thread(target=external_data_thread, args=(gdmb_calc, 2.5,))
                thread.daemon = True
                thread.start()
                gdmb_calc.plot_figure()
                # Check that scaling is applied
                assert gdmb_calc.scale_target == "N"
            except Exception:
                print("Test Dynamic Mode Negative Failed.")
                raise Exception("Test Dynamic Mode Negative Failed.")

    def test_static_mode_positive(self):
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=True, dynamic=False,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            raise_without_traceback(type(e), e)
        else:
            try:
                gdmb_calc.batch_receive_data(target_y_list=target_np, measurement_y_list=measurement_np)
                gdmb_calc.plot_figure()
                # Check that scaling is applied
                assert gdmb_calc.scale_target == "Y"
                # Check that scaled data is within target bounds
                lower_bound_bools = np.less(gdmb_calc.lower_tar, gdmb_calc.scaled_data)
                upper_bound_bools = np.greater(gdmb_calc.upper_tar, gdmb_calc.scaled_data)
                lb_set = set(lower_bound_bools)
                ub_set = set(upper_bound_bools)
                assert False not in lb_set and False not in ub_set
            except Exception:
                print("Test Static Mode Positive Failed.")
                raise Exception("Test Static Mode Positive Failed.")

    def test_static_mode_negative(self):
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\negative_data", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\negative_data", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=False, dynamic=False,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            raise_without_traceback(type(e), e)
        else:
            try:
                gdmb_calc.batch_receive_data(target_y_list=target_np, measurement_y_list=measurement_np)
                gdmb_calc.plot_figure()
                assert gdmb_calc.scale_target == "N"
            except Exception:
                print("Test Static Mode Negative Failed.")
                raise Exception("Test Static Mode Negative Failed.")
