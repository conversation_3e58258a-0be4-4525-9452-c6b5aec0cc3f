"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 17, 2024
"""

import time
import hashlib
import multiprocessing
from queue import Queue
from io import BytesIO
import requests
from flask import Flask, jsonify, Response
import unittest
import numpy as np
import pandas as pd
import os
import threading
from pxl.gdmb_calc.gdmb_calc import GDMBCalculator
from pxl.dlb.watermarks import WATERMARK_DLB_BASE64
from pxl.util.utils import raise_without_traceback
from pxl.util.pusher import BytesIOPusher


app = Flask(__name__)

SERVER_HOST = '127.0.0.1'
SERVER_PORT = 5000

data_complete = threading.Event()
data_queue = Queue()

saved_figure = os.path.abspath(os.path.join(os.path.dirname(__file__), "sample_gdmb_fig_test_dynamic_mode.png"))


class SimpleListPusher(BytesIOPusher):
    # Simulate generating and sending figure data
    def __init__(self, queue: Queue):
        self._queue = queue

    def push(self, buffer: BytesIO):
        buffer.seek(0)
        self._queue.put(buffer.read())


@app.route('/start', methods=['POST'])
def start_service():
    # Read csv file and move contents to numpy arrays
    target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'target_data.csv'))
    measurement_file: str = os.path.abspath(
        os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'measurement_data.csv'))
    target_df = pd.read_csv(target_file, names=['Section', "Nits"])
    target_np = target_df.Nits.to_numpy()
    data_frame = pd.read_csv(measurement_file)
    measurement_np = data_frame.Y.to_numpy()

    # Thread function call
    def external_data_thread(calculator, interval):
        print("External data thread started")
        for i in range(measurement_np.size):  # Number of expected measurements
            target_y = target_np[i * 4]  # Target Step = 20
            measurement_y = measurement_np[i]  # Measurement Step = 80
            print("Target: " + str(target_y) + " Measurement: " + str(measurement_y))
            calculator.receive_data(i, target_y, measurement_y, redraw=True, interval=interval)
        calculator.done()
        data_complete.set()

    try:
        gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np),
                                                   debug=False,
                                                   dynamic=True,
                                                   show_figure=False,
                                                   bytesio_pusher=SimpleListPusher(data_queue),
                                                   logo=WATERMARK_DLB_BASE64)
    # run function call here
    except Exception as e:
        raise_without_traceback(type(e), e)
        return jsonify({'error': str(e)}), 500
    else:
        thread = threading.Thread(target=external_data_thread, args=(gdmb_calc, 2.5,))
        thread.start()
        gdmb_calc.plot_figure(save_figure=saved_figure)
        thread.join()
    return jsonify({'status': 'processing complete'}), 200


@app.route('/figure', methods=['GET'])
def get_figure_data():
    if not data_queue.empty():
        fig = data_queue.get()
        hash_value = hashlib.md5(fig).hexdigest()
        print(f"get_figure_data(): Hash of fig: {hash_value}")
        return Response(fig, content_type='image/png')
    elif data_complete.is_set():
        return jsonify({'status': 'complete'}), 200
    else:
        return jsonify({'status': 'waiting'}), 202


def run_flask_app():
    app.run(host=SERVER_HOST, port=SERVER_PORT, use_reloader=False)


def calculate_hash(file_path, hash_algorithm='md5'):
    hash_func = hashlib.new(hash_algorithm)
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):  # read data chunk by chunk
            hash_func.update(chunk)
    return hash_func.hexdigest()


class TestGDMBCalculatorPlotWebSrv(unittest.TestCase):

    def test_dynamic_mode_websrv_flask_based(self):
        # Prepare data
        fig_received = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "sample_gdmb_fig_test_dynamic_mode_recv.png")
        )

        # Start Flask app in a separate process
        flask_process = multiprocessing.Process(target=run_flask_app)
        flask_process.start()

        time.sleep(2)  # Allow Flask to start

        # Send start request to Flask server
        response = requests.post(f'http://{SERVER_HOST}:{SERVER_PORT}/start')
        self.assertEqual(response.status_code, 200)
        while True:
            response = requests.get(f'http://{SERVER_HOST}:{SERVER_PORT}/figure')

            # Debugging: Log response content
            print(f"Response status: {response.status_code}, Content-Type: {response.headers.get('Content-Type')}")

            if response.status_code == 200:
                # Handle JSON response
                try:
                    json_data = response.json()
                    print(f"json_data.get('status')={json_data.get('status')}")
                    if json_data.get('status') == 'complete':
                        break
                except requests.exceptions.JSONDecodeError:
                    if response.headers.get('Content-Type') == 'image/png':
                        print("Saving received PNG image")
                        image_data = BytesIO(response.content)

                        # hash
                        hash_value = hashlib.md5(image_data.read()).hexdigest()
                        print(f"Hash of image_data: {hash_value}")
                        image_data.seek(0)
                    else:
                        print("Received non-PNG data, skipping...")
            elif response.status_code == 202:
                # Waiting status
                time.sleep(0.5)
            else:
                # Unexpected response
                self.fail(f"Unexpected response: {response.status_code} - {response.text}")

        # Fetch figure data
        with open(fig_received, 'wb') as f:
            # save figure
            bytes = image_data.read()
            image_data.seek(0)
            f.write(bytes)

        # have to terminate the flask app otherwise it runs forever
        flask_process.terminate()
        flask_process.join()
        print("Receiving data finished")

        self.assertTrue(os.path.getsize(fig_received) > 0)
        self.assertTrue(os.path.getsize(saved_figure) > 0)


if __name__ == '__main__':
    unittest.main()
