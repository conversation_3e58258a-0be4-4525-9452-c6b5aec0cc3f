#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import unittest
import numpy as np
from unittest.mock import Mock

from pxl.calc.base import BaseCalculator, CalculatorConfig, CalculatorState, CalculatorFactory
from pxl.calc.data_manager import DataManager, DataBuffer
from pxl.calc.computation import DeltaEComputation, GDMBComputation
from pxl.calc.state_manager import StateManager
from pxl.calc.de_calculator import DeltaECalculatorRefactored, DeltaECalculatorConfig, ScaleTarget
from pxl.calc.gdmb_calculator import GDMBCalculatorRefactored, GDMBCalculatorConfig
from pxl.calc.compatibility import DeltaECalculatorAdapter
from pxl.de_calc.de_calc import DeltaECalculator
from pxl.util.logging_config import get_logger


class TestRefactoredComponents(unittest.TestCase):
    """Test cases for refactored calculator components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
    
    def test_data_manager(self):
        """Test DataManager functionality."""
        data_manager = DataManager()
        
        # Test buffer operations
        self.assertTrue(data_manager.buffer.empty())
        
        # Test reference white
        ref_white = np.array([0.3, 0.3, 0.3])
        data_manager.set_reference_white(ref_white)
        np.testing.assert_array_equal(data_manager.reference_white, ref_white)
        
        # Test scale multiplier
        data_manager.set_scale_multiplier(2.0)
        self.assertEqual(data_manager.scale_multiplier, 2.0)
        
        # Test XYZ validation
        valid_xyz = np.array([0.3, 0.3, 0.3])
        data_manager.validate_xyz_data(valid_xyz)  # Should not raise
        
        # Test invalid XYZ
        with self.assertRaises(Exception):
            data_manager.validate_xyz_data(np.array([0.3, 0.3]))  # Wrong shape
    
    def test_delta_e_computation(self):
        """Test DeltaE computation engine."""
        computation = DeltaEComputation()
        
        # Set reference white
        ref_white = np.array([0.3, 0.3, 0.3])
        computation.set_reference_white(ref_white)
        
        # Test computation
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        
        delta_e = computation.compute(target_xyz, measured_xyz)
        self.assertIsInstance(delta_e, float)
        self.assertGreater(delta_e, 0)
        
        # Test statistics
        self.assertEqual(computation.computation_count, 1)
        self.assertEqual(computation.max_delta_e, delta_e)
        self.assertEqual(computation.average_delta_e, delta_e)
    
    def test_gdmb_computation(self):
        """Test GDMB computation engine."""
        computation = GDMBComputation(tmax=500.0)
        
        # Test computation
        max_flag = computation.compute(100.0, 95.0)
        self.assertIsInstance(max_flag, bool)
        
        # Test data storage
        self.assertEqual(len(computation.target_data), 1)
        self.assertEqual(len(computation.measurement_data), 1)
        self.assertEqual(computation.target_data[0], 100.0)
        self.assertEqual(computation.measurement_data[0], 95.0)
        
        # Test scale factor calculation
        scale_factor = computation.calculate_scale_factor()
        self.assertIsInstance(scale_factor, float)
        self.assertGreater(scale_factor, 0)
    
    def test_state_manager(self):
        """Test StateManager functionality."""
        state_manager = StateManager()
        
        # Test state
        self.assertEqual(state_manager.state, CalculatorState.INITIALIZED)
        state_manager.set_state(CalculatorState.RUNNING)
        self.assertEqual(state_manager.state, CalculatorState.RUNNING)
        
        # Test flags
        self.assertFalse(state_manager.get_flag('test_flag'))
        state_manager.set_flag('test_flag', True)
        self.assertTrue(state_manager.get_flag('test_flag'))
        
        # Test counters
        self.assertEqual(state_manager.get_counter('test_counter'), 0)
        state_manager.increment_counter('test_counter', 5)
        self.assertEqual(state_manager.get_counter('test_counter'), 5)
        
        # Test reset
        state_manager.reset()
        self.assertEqual(state_manager.state, CalculatorState.INITIALIZED)
        self.assertFalse(state_manager.get_flag('test_flag'))
    
    def test_refactored_delta_e_calculator(self):
        """Test refactored DeltaE calculator."""
        config = DeltaECalculatorConfig(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=ScaleTarget.AUTO,
            target_max=500.0,
            dynamic=False,
            show_figure=False
        )
        
        calculator = DeltaECalculatorRefactored(config)
        
        # Test initialization
        calculator.initialize()
        self.assertEqual(calculator.state, CalculatorState.INITIALIZED)
        
        # Test reference white setting
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        calculator.set_reference_white(target_xyz, measured_xyz)
        
        # Test data processing
        calculator.receive_data(0, target_xyz, measured_xyz)
        
        # Test report
        report = calculator.get_report()
        self.assertIsNotNone(report)
        
        # Test cleanup
        calculator.cleanup()
    
    def test_refactored_gdmb_calculator(self):
        """Test refactored GDMB calculator."""
        config = GDMBCalculatorConfig(
            tmax=500.0,
            debug=False,
            dynamic=False,
            show_figure=False
        )
        
        calculator = GDMBCalculatorRefactored(config)
        
        # Test initialization
        calculator.initialize()
        self.assertEqual(calculator.state, CalculatorState.INITIALIZED)
        
        # Test data processing
        max_flag = calculator.receive_data(0, 100.0, 95.0)
        self.assertIsInstance(max_flag, bool)
        
        # Test scale factor
        scale_factor = calculator.scale_factor()
        self.assertIsInstance(scale_factor, float)
        
        # Test report
        report = calculator.get_report()
        self.assertIsInstance(report, dict)
        self.assertIn('tmax', report)
        self.assertIn('mmax', report)
        
        # Test cleanup
        calculator.cleanup()
    
    def test_backward_compatibility_adapter(self):
        """Test backward compatibility adapter."""
        # Test adapter creation
        adapter = DeltaECalculatorAdapter(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculatorAdapter.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        
        # Test interface compatibility
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        
        adapter.set_reference_white(target_xyz, measured_xyz)
        adapter.receive_data(0, target_xyz, measured_xyz)
        report = adapter.get_report()
        
        self.assertIsNotNone(report)
        adapter.cleanup()
    
    def test_factory_method_compatibility(self):
        """Test factory method for backward compatibility."""
        # Test original factory method
        calc_v2 = DeltaECalculator.create_v2(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculator.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        self.assertIsNotNone(calc_v2)
        
        # Test refactored factory method
        calc_refactored = DeltaECalculator.create_refactored(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculator.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        self.assertIsNotNone(calc_refactored)
        
        # Test that both have similar interfaces
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        
        calc_refactored.set_reference_white(target_xyz, measured_xyz)
        calc_refactored.receive_data(0, target_xyz, measured_xyz)
        report = calc_refactored.get_report()
        
        self.assertIsNotNone(report)
        calc_refactored.cleanup()
    
    def test_calculator_factory(self):
        """Test calculator factory."""
        # Test factory registration
        available_calculators = CalculatorFactory.list_calculators()
        self.assertIn('delta_e_refactored', available_calculators)
        self.assertIn('gdmb_refactored', available_calculators)
        
        # Test factory creation (would need proper config)
        # This is more of an integration test
        pass


class TestSeparationOfConcerns(unittest.TestCase):
    """Test that the refactored design properly separates concerns."""
    
    def test_data_manager_isolation(self):
        """Test that DataManager only handles data operations."""
        data_manager = DataManager()
        
        # DataManager should not have plotting methods
        self.assertFalse(hasattr(data_manager, 'plot'))
        self.assertFalse(hasattr(data_manager, 'show'))
        self.assertFalse(hasattr(data_manager, 'savefig'))
        
        # DataManager should have data-related methods
        self.assertTrue(hasattr(data_manager, 'validate_xyz_data'))
        self.assertTrue(hasattr(data_manager, 'set_reference_white'))
        self.assertTrue(hasattr(data_manager, 'buffer'))
    
    def test_computation_isolation(self):
        """Test that computation engines only handle calculations."""
        de_computation = DeltaEComputation()
        
        # Computation should not have plotting methods
        self.assertFalse(hasattr(de_computation, 'plot'))
        self.assertFalse(hasattr(de_computation, 'show'))
        
        # Computation should have calculation methods
        self.assertTrue(hasattr(de_computation, 'compute'))
        self.assertTrue(hasattr(de_computation, 'validate_inputs'))
    
    def test_state_manager_isolation(self):
        """Test that StateManager only handles state operations."""
        state_manager = StateManager()
        
        # StateManager should not have plotting or computation methods
        self.assertFalse(hasattr(state_manager, 'plot'))
        self.assertFalse(hasattr(state_manager, 'compute'))
        
        # StateManager should have state-related methods
        self.assertTrue(hasattr(state_manager, 'get_flag'))
        self.assertTrue(hasattr(state_manager, 'set_flag'))
        self.assertTrue(hasattr(state_manager, 'get_counter'))


if __name__ == '__main__':
    unittest.main()
