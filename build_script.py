import argparse
import os
import sys

import build


def build_wheel(package_path, output_dir):
    try:
        # Create build object
        build_main = build.ProjectBuilder(package_path)
        # Generate the wheel package and specify the output directory
        wheel_path = build_main.build('wheel', output_directory=output_dir)
        print(f"Wheel package has been moved to {output_dir}")
    except Exception as e:
        print(f"Error occurred while building the wheel package: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Create argument parser
    parser = argparse.ArgumentParser(description="Build and move wheel packages.")
    parser.add_argument("output_dir", help="Directory to move the built wheel packages to.")

    # Parse command-line arguments
    args = parser.parse_args()

    # Retrieve the absolute path of the project root
    project_root = os.path.abspath(os.path.dirname(__file__))

    # Set the target path for wheel packages
    wheel_path = args.output_dir

    # List of packages to build
    packages = {'pxl': "setup.py"}

    # Iterate over packages and build each one
    for package, setup_file in packages.items():
        print(f">>>building {package}")
        package_path = project_root

        # Check if setup.py exists in the package path
        if os.path.isfile(os.path.join(package_path, setup_file)):
            try:
                # Configure PYTHONPATH so packages can reference each other
                env = os.environ.copy()
                env['PYTHONPATH'] = os.path.join(project_root, 'src')
                build_wheel(package_path, wheel_path)
            except Exception as e:
                print(f"Error occurred while setting up the environment: {e}")
                sys.exit(1)
        else:
            print(f"No setup.py found in {package_path}")
            sys.exit(1)
