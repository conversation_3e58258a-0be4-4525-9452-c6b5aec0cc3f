#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from setuptools import setup, find_packages

# Get version from package
about = {}
here = os.path.abspath(os.path.dirname(__file__))
with open(os.path.join(here, 'src', 'pxl', '__init__.py'), 'r', encoding='utf-8') as f:
    exec(f.read(), about)

# Read the README file for the long description
with open('README.md', 'r', encoding='utf-8') as f:
    long_description = f.read()

setup(
    name='pxl',
    version='0.2.4',
    long_description=long_description,
    author="<PERSON>, <PERSON>",
    author_email='<EMAIL>, <EMAIL>',
    packages=find_packages(where='src'),
    package_dir={'': 'src'},
    install_requires=[
        "numpy>=1.26.2,<2",
        "scipy>=1.12.0,<2",
        "matplotlib>=3.8.4,<3.9",
        "openpyxl>=3.1.2,<4",
        "pandas>=2.2.2,<3",
    ],
    extras_require={
        'build': [
            'build>=1.2.1',
            'setuptools>=68.2.0',
            'wheel>=0.41.2',
        ],
        'test': [
            'parameterized>=0.9.0',
            'flask',
            'requests',
            'coverage',
        ]
    },
    python_requires=">=3.11, <4",
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Dolby Licensees',
        'License :: Dolby Laboratories :: Proprietary',
        'Programming Language :: Python :: 3.11',
        'Operating System :: OS Independent',
        'Topic :: Utilities',
    ],
    keywords='PXL, Dolby',
)
