#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: May 29, 2024
"""


import numpy as np


def atan2d(inY, inX):
    """Calculate the arctangent of inY/inX in degrees."""
    out = np.arctan2(inY, inX) * 180 / np.pi
    out = out + (out < 0) * 360
    return out


def sind(in_angle):
    """Calculate the sine of in_angle in degrees."""
    return np.sin(in_angle * np.pi / 180)


def cosd(in_angle):
    """Calculate the cosine of in_angle in degrees."""
    return np.cos(in_angle * np.pi / 180)


def xyz2lab(xyz, wp):
    """Convert XYZ color space to CIELAB color space."""
    xyz = xyz / wp
    fxyz_n = np.maximum(0, xyz) ** (1 / 3)
    k = 841 / 108
    L = xyz <= 216 / 24389
    fxyz_n[L] = k * xyz[L] + 16 / 116
    lab = np.zeros_like(xyz)
    lab[:, 0] = 116 * fxyz_n[:, 1] - 16
    lab[:, 1] = 500 * (fxyz_n[:, 0] - fxyz_n[:, 1])
    lab[:, 2] = 200 * (fxyz_n[:, 1] - fxyz_n[:, 2])
    return lab


def DE2000(XYZ1, XYZ2, XYZWP):
    # Reshape the input XYZ arrays to nx3
    XYZ1 = XYZ1.reshape(-1, 3)
    XYZ2 = XYZ2.reshape(-1, 3)

    # Convert XYZ to LAB color space
    Lab1 = xyz2lab(XYZ1, XYZWP)
    Lab2 = xyz2lab(XYZ2, XYZWP)

    # Compute CIELAB Chroma for both LAB arrays
    C1 = np.sqrt(Lab1[:, 1]**2 + Lab1[:, 2]**2)
    C2 = np.sqrt(Lab2[:, 1]**2 + Lab2[:, 2]**2)

    # Compute Lab Prime values
    mC = (C1 + C2) / 2
    G = 0.5 * (1 - np.sqrt((mC**7) / (mC**7 + 25**7)))
    LabP1 = np.column_stack([Lab1[:, 0], Lab1[:, 1] * (1 + G), Lab1[:, 2]])
    LabP2 = np.column_stack([Lab2[:, 0], Lab2[:, 1] * (1 + G), Lab2[:, 2]])

    # Compute Chroma for Lab Prime values
    CP1 = np.sqrt(LabP1[:, 1]**2 + LabP1[:, 2]**2)
    CP2 = np.sqrt(LabP2[:, 1]**2 + LabP2[:, 2]**2)

    # Compute Hue Angle for Lab Prime values
    hP1t = atan2d(LabP1[:, 2], LabP1[:, 1])
    hP2t = atan2d(LabP2[:, 2], LabP2[:, 1])

    # Adjust hue angles if needed
    hP1 = hP1t + ((hP1t < hP2t) & (np.abs(hP1t - hP2t) > 180)) * 360
    hP2 = hP2t + ((hP1t > hP2t) & (np.abs(hP1t - hP2t) > 180)) * 360

    # Compute Delta values
    DLP = LabP1[:, 0] - LabP2[:, 0]
    DCP = CP1 - CP2
    DhP = hP1 - hP2
    DHP = 2 * np.sqrt(CP1 * CP2) * np.sin(np.radians(DhP / 2))

    # Compute arithmetic mean of LCh' values
    mLP = (LabP1[:, 0] + LabP2[:, 0]) / 2
    mCP = (CP1 + CP2) / 2
    mhP = (hP1 + hP2) / 2

    # Compute weighting functions
    SL = 1 + (0.015 * (mLP - 50)**2) / np.sqrt(20 + (mLP - 50)**2)
    SC = 1 + 0.045 * mCP
    T = 1 - 0.17 * cosd(mhP - 30) + 0.24 * cosd(2 * mhP) + 0.32 * cosd(3 * mhP + 6) - 0.2 * cosd(4 * mhP - 63)
    SH = 1 + 0.015 * mCP * T

    # Compute rotation function
    RC = 2 * np.sqrt((mCP**7) / (mCP**7 + 25**7))
    DTheta = 30 * np.exp(-((mhP - 275) / 25)**2)
    RT = -sind(2 * DTheta) * RC

    # Parametric factors
    kL = kC = kH = 1

    # Calculate weighted DLP, DCP, and DHP components
    DLP_W = DLP / (kL * SL)
    DCP_W = DCP / (kC * SC)
    DHP_W = DHP / (kH * SH)

    # Compute dE2000
    DE = np.sqrt(DLP_W**2 + DCP_W**2 + DHP_W**2 + RT * DCP_W * DHP_W)

    # return DE, DLP_W, DCP_W, DHP_W
    return DE.item(), DLP_W.item(), DCP_W.item(), DHP_W.item()
