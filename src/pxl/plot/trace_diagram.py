#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: November 13, 2024
"""

import numpy as np


class TraceDiagram:
    """
    Trace Diagram subplot class.

    Parameters:
        ax (matplotlib.axes.Axes): The axes object where the table will be drawn.
        tmax (float): Max Target Data value (nits).
        width (float): Width of table cells.
        height (float): Height of table cells.

    """
    def __init__(self, ax, tmax: float,
                 width: float = 0.32, height: float = 0.07):
        self.ax = ax
        self.tmax = tmax
        self.width = width
        self.height = height
        self.target_plot = None
        self.measurement_plot = None
        self.upper_plot = None
        self.lower_plot = None
        self.marker_size = 2
        self.x_label = "DM Metadata Sections (step=80)"
        self.y_label = "Luminance Intensity (nits)"

    def figure_assemble(self):
        """
        Add X-axis and Y-axis labels to the plot.
        """
        self.ax.set_xlabel(self.x_label)
        self.ax.set_ylabel(self.y_label)

    def trace_plot(self, md_sections: np.ndarray, measurement_data: np.ndarray, result_color: str = "black"):
        """
        Updates plot data.
        """
        self.measurement_plot, = self.ax.plot(md_sections, measurement_data, color=result_color,
                                              marker="o", markersize=self.marker_size)
        arr = []
        for i in range(md_sections.size):
            arr.append(self.tmax)
        tmax_arr = np.asarray(arr)
        self.target_plot, = self.ax.plot(md_sections, tmax_arr, color="blue", linestyle="--",
                                         markersize=self.marker_size)

    def final_plot(self, md_sections: np.ndarray, measurement_data: np.ndarray,
                   upper_tar: np.ndarray, lower_tar: np.ndarray, result_color: str = "black"):
        """
        Creates final plot for showing end result.
        """
        self.ax.clear()
        self.measurement_plot, = self.ax.plot(md_sections, measurement_data, color=result_color,
                                              marker="o", markersize=self.marker_size)
        self.upper_plot, = self.ax.plot(md_sections, upper_tar, color='gray',
                                        linestyle='--', dashes=(3, 5), markersize=self.marker_size)
        self.lower_plot, = self.ax.plot(md_sections, lower_tar, color='gray',
                                        linestyle='--', dashes=(3, 5), markersize=self.marker_size)
