#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Jun 18, 2024
"""

import matplotlib.pyplot as plt
import numpy as np


class DynamicTable:
    def __init__(self, ax, columns, column_width=0.2, row_height=0.04, font_size=8):
        """Initialize the DynamicTable for dynamically updating a table in a Matplotlib figure.

        Args:
            ax (matplotlib.axes.Axes): The axes object where the table will be drawn.
            columns (list): List of column headers for the table.
            column_width (float): Uniform width for each column. Defaults to 0.2.
            row_height (float): Height for each row. Defaults to 0.04.
            font_size (int): Font size for table text. Defaults to 8.

        Returns:
            None
        """
        self._ax = ax
        self._columns = columns
        self._column_width = column_width  # Store column width
        self._row_height = row_height  # Store row height
        self._font_size = font_size
        self._data = [np.full(len(columns), '').tolist()]  # Initialize with empty strings
        self._first_real_data_added = False

        # Initialize the table with initial placeholder data
        self._table = self._ax.table(
            cellText=self._data, colLabels=self._columns,
            loc='upper left', cellLoc='center',
        )
        self._set_column_widths()
        self._table.scale(1, 1.5)
        self._ax.axis('off')  # Hide axes for aesthetics
        self._update_table_properties()

    def add_external_data(self, new_data, text_colors=None, bg_colors=None, redraw: bool = True):
        """Add a new row of data to the table from an external source.

        Args:
            new_data (list): Data matching the number of columns in the table.
            text_colors (list): List of color codes for the text in each column. Defaults to None.
            bg_colors (list): List of background color codes for each column. Defaults to None.
            redraw (bool): Whether to redraw the table. Defaults to True.

        Raises:
            AssertionError: If the number of colors doesn't match the number of columns.
        """

        if new_data:
            # Ensure that colors list is provided and matches the number of columns
            if text_colors is None:
                text_colors = ['black'] * len(self._columns)  # Default to black if no colors are provided
            if bg_colors is None:
                bg_colors = ['white'] * len(self._columns)  # Default to white for background if no colors are provided
            assert len(text_colors) == len(self._columns), "Number of text colors must match number of columns."
            assert len(bg_colors) == len(self._columns), "Number of background colors must match number of columns."

            # Append new data to the list
            self._data.append(new_data)  # Append new data to the list
            row_index = len(self._data) - 1
            # Add new cells to the table for the new row
            for col_index, (value, text_color, bg_color) in enumerate(zip(new_data, text_colors, bg_colors)):
                cell = self._table.add_cell(
                    row_index,
                    col_index, text=str(value),
                    width=self._column_width, height=self._row_height,
                    loc='center',
                )
                cell.get_text().set_color(text_color)
                cell.set_facecolor(bg_color)

            self._update_table_properties()  # Update table properties every time new data is added

            if redraw:
                self._ax.figure.canvas.draw_idle()  # Redraw the table to reflect changes

    def _set_column_widths(self):
        """
        Sets uniform width for each column in the table.
        """
        for i in range(len(self._columns)):
            for row in range(len(self._data) + 1):  # Including header row
                self._table._cells[(row, i)].set_width(self._column_width)

    def _update_table_properties(self):
        """
        Update table properties such as row height and font size based on the current size of the axes.
        This method adjusts the font size and row height proportionally if the total required height exceeds
        the axes height.
        """
        if not self._data:
            return  # No data to display, so skip updating

        # Calculate the total number of rows including the header
        num_rows = len(self._data) + 1  # +1 for the header

        # Calculate the proportion of the total height required by all rows
        required_height_proportion = num_rows * self._row_height

        # Get the total height of the axes in inches
        bbox = self._ax.get_window_extent().transformed(self._ax.figure.dpi_scale_trans.inverted())
        total_height_in_inches = bbox.height

        # Calculate the actual required height in inches
        required_height_inches = required_height_proportion * total_height_in_inches

        # Determine if the required height exceeds the available height of the axes
        if required_height_inches > total_height_in_inches:
            # Adjust if the required height exceeds the total available height
            inches_per_pt = 1.0 / 72  # matplotlib uses 72 dpi for points
            total_height_pts = total_height_in_inches / inches_per_pt  # convert inches to points
            new_row_height = min(1.0, 1.0 / num_rows)  # calculate row height
            new_font_size = min(
                self._font_size,
                int(new_row_height * 0.7 * total_height_pts)
            )  # scale font size based on row height but not more than the default
        else:
            # Use initial settings if they fit
            new_row_height = self._row_height
            new_font_size = self._font_size

        # Update row height and font size for each cell in the table
        for (row, col), cell in self._table.get_celld().items():
            cell.set_height(new_row_height)
            cell.get_text().set_fontsize(new_font_size)


if __name__ == "__main__":
    # test code
    fig, ax = plt.subplots()
    dt = DynamicTable(ax, ['X', 'Y', 'Z'])
    dt.add_external_data([1, 2, 3])
    dt.add_external_data([4, 5, 6])

    plt.show()
