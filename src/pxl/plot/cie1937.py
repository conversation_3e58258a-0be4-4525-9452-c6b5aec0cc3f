#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: May 21, 2024
"""

from typing import Iterable

import matplotlib.lines as mlines
import numpy as np
from matplotlib.artist import Artist

from pxl.clrspc.xyz import XYZ2xyz, visualize_XYZ


class CIE1931ChromaticityDiagram:
    def __init__(self, ax):
        """Initialize the CIE1931ChromaticityDiagram with a matplotlib axes.

        Args:
            ax (matplotlib.axes.Axes): The axis object where the chromaticity diagram will be drawn.

        Returns:
            None

        Note:
            This constructor sets up the environment for the chromaticity diagram including plotting
            the outline, initializing data stores for colors, and preparing scatter plots for dynamic updates.
        """
        # the subplot(axes) where the chromaticity diagram will be drawn
        self._ax = ax
        # Initialize lists to store dynamic color data and a lock to prevent race conditions
        self._data_b_store = []
        self._data_a_store = []
        # Initialize the chromaticity diagram and scatter plot objects
        self._chroma_color_sets = []
        self._plot_cie_diagram_outline()  # Call this method to plot the outline
        # List to store legend handles
        self._legend_handles = []
        # Scatters to store the color points
        self._scatter_a = self._ax.scatter([], [], s=25, facecolors='none', edgecolors='red', linewidth=0.5, marker='s')
        self._scatter_b = self._ax.scatter([], [], s=10, facecolors='none', edgecolors='blue', linewidth=1, marker='.')

    def add_b_color(self, XYZ, redraw: bool = True) -> Iterable[Artist]:
        """Add a new color measurement data point in the form of XYZ values to the scatter plot for B.

        Args:
            XYZ (array-like): The XYZ color space values to be added.
            redraw (bool): Whether to redraw the chart after adding the new color. Defaults to True.

        Returns:
            Iterable[Artist]: The updated scatter plot artist object for color B.
        """
        # Add new B color to the queue
        self._data_b_store.append(XYZ)
        xy_measured = [XYZ2xyz(v)[0:2] for v in self._data_b_store]
        self._scatter_b.set_offsets(xy_measured)
        RGB_measured = [visualize_XYZ(XYZ=XYZ2xyz(v), max_Y=1.0) for v in self._data_b_store]
        self._scatter_b.set_edgecolors(RGB_measured)
        self._scatter_b.set_alpha(1)

        if redraw:
            self._ax.figure.canvas.draw_idle()
        return self._scatter_b,

    def add_a_color(self, XYZ, redraw: bool = True) -> Iterable[Artist]:
        """Add a new color measurement data point in the form of XYZ values to the scatter plot for A.

        Args:
            XYZ (array-like): The XYZ color space values to be added.
            redraw (bool): Whether to redraw the chart after adding the new color. Defaults to True.

        Returns:
            Iterable[Artist]: The updated scatter plot artist object for color A.
        """
        # Add new A color to the queue
        self._data_a_store.append(XYZ)
        xy_target = [XYZ2xyz(v)[0:2] for v in self._data_a_store]
        self._scatter_a.set_offsets(xy_target)
        RGB_target = [visualize_XYZ(XYZ=XYZ2xyz(v), max_Y=1.0) for v in self._data_a_store]
        self._scatter_a.set_edgecolors(RGB_target)
        self._scatter_a.set_alpha(1)

        if redraw:
            self._ax.figure.canvas.draw_idle()

        return self._scatter_a,

    def set_chroma_color_sets(self, chroma_color_sets):
        """Set and visualize predefined chroma color sets on the CIE 1931 chromaticity diagram.

        Args:
            chroma_color_sets (list): List of tuples containing (label, style, color, coordinates).
                Each tuple contains:
                - label (str): Name of the color set
                - style (str): Line style
                - color (str): Color of the line
                - coordinates (list): List of chromaticity coordinates

        Raises:
            ValueError: If there are less than three points in any chroma color set.
        """
        # Set the chroma color sets for the diagram
        self._chroma_color_sets = chroma_color_sets
        for label, style, color, chroma_colors in self._chroma_color_sets:
            for point in chroma_colors:
                xy = point[0:2]
                self._ax.plot(xy[0], xy[1], 'o', color=[1, 1, 1], markersize=1, label=f'{point}')
            self._connect_points_with_dashed_line(chroma_colors[0:3], style=style, linecolor=color, linewidth=0.5,
                                                  label=label)

    def _plot_cie_diagram_outline(self):
        # Plot the CIE 1931 chromaticity diagram outline on self._ax

        # wavelengths = np.arange(380, 781, 1)
        # xyz = np.array([colour.wavelength_to_XYZ(wl) for wl in wavelengths])
        # xy = np.array([colour.XYZ_to_xy(point) for point in xyz])
        xy = [
            [0.17411223, 0.00496373],
            [0.17380077, 0.00491541],
            [0.17293426, 0.00478115],
            [0.17174121, 0.00493933],
            [0.17030099, 0.0057885],
            [0.16852466, 0.00718404],
            [0.16644633, 0.0089644],
            [0.16382843, 0.01138487],
            [0.1603096, 0.01449138],
            [0.1556051, 0.01860861],
            [0.14833682, 0.0252474],
            [0.13912068, 0.03520057],
            [0.12666216, 0.05342592],
            [0.10959432, 0.08684251],
            [0.08708243, 0.14431658],
            [0.05931583, 0.23525374],
            [0.03175647, 0.36359769],
            [0.0104757, 0.51340425],
            [0.00464571, 0.67589846],
            [0.02224421, 0.77962992],
            [0.05217669, 0.82516354],
            [0.09793975, 0.83159267],
            [0.14677322, 0.81039461],
            [0.1928761, 0.78162922],
            [0.23688472, 0.74852447],
            [0.28012894, 0.71172473],
            [0.32306627, 0.6723674],
            [0.36595936, 0.63137908],
            [0.40873626, 0.58960687],
            [0.45106494, 0.54776604],
            [0.49240498, 0.50661492],
            [0.5320656, 0.46709136],
            [0.56925682, 0.43010197],
            [0.60293279, 0.39649663],
            [0.63152094, 0.36802601],
            [0.65566918, 0.34401829],
            [0.67471951, 0.32509518],
            [0.6894263, 0.31041401],
            [0.70060606, 0.2993007],
            [0.70923099, 0.29071862],
            [0.71511705, 0.28484511],
            [0.71903294, 0.28093495],
            [0.72155452, 0.27841951],
            [0.72370192, 0.27628184],
            [0.72546678, 0.27452998],
            [0.72697497, 0.27302503],
            [0.72827173, 0.27172827],
            [0.72936095, 0.27063905],
            [0.73023395, 0.26976605],
            [0.73089625, 0.26910375],
            [0.73146705, 0.26853295],
            [0.7319933, 0.2680067],
            [0.73244282, 0.26755718],
            [0.73285865, 0.26714135],
            [0.73328118, 0.26671882],
            [0.7336833, 0.2663167],
            [0.7340473, 0.2659527],
            [0.73428645, 0.26571355],
            [0.73443771, 0.26556229],
            [0.73455952, 0.26544048],
            [0.73462109, 0.26537891],
            [0.73467338, 0.26532662],
            [0.73469005, 0.26530995],
            [0.73469002, 0.26530998],
            [0.73469001, 0.26530999],
            [0.73468998, 0.26531002],
            [0.73468996, 0.26531004],
            [0.73469005, 0.26530995],
            [0.73469001, 0.26530999],
            [0.73468999, 0.26531001],
            [0.73468996, 0.26531004],
            [0.73469008, 0.26530992],
            [0.73468999, 0.26531001],
            [0.73469, 0.26531],
            [0.73469, 0.26531],
            [0.73468999, 0.26531001],
            [0.73469, 0.26531],
            [0.73469002, 0.26530998],
            [0.73469, 0.26531],
            [0.73469003, 0.26530997],
            [0.73468997, 0.26531003],
            [0.73468995, 0.26531005],
            [0.73469009, 0.26530991],
            [0.73469, 0.26531],
            [0.73468999, 0.26531001],
            [0.73469001, 0.26530999],
            [0.73468998, 0.26531002]
        ]

        # ensure a close loop
        xy = np.append(xy, [xy[0]], axis=0)

        # plot the chromaticity
        self._ax.plot(xy[:, 0], xy[:, 1], color='gray', linewidth=0.5, linestyle='--', alpha=0.7)

        # set boundaries
        self._ax.set_xlim([0, 0.8])
        self._ax.set_ylim([0, 0.8])
        self._ax.set_xlabel('CIE x', fontsize=7)
        self._ax.set_ylabel('CIE y', fontsize=7)

        # Customize tick labels font size
        self._ax.tick_params(axis='both', which='major', labelsize=6)

        # set title
        self._ax.set_title('CIE 1931 Chromaticity Diagram')

    def _connect_points_with_dashed_line(self, points, style=':', linecolor="gray", linewidth=0.5, label=None):
        # Connect points with a dashed line
        xy_coords = [point[0:2] for point in points]
        xy_coords.append(xy_coords[0])
        x_coords, y_coords = zip(*xy_coords)
        self._ax.plot(x_coords, y_coords, style, color=linecolor, linewidth=linewidth, label=label)
        # If a label is provided, update the legend
        if label:
            self._legend_handles.append(mlines.Line2D([], [], color=linecolor, linestyle=':', linewidth=1, label=label))
            self._ax.legend(handles=self._legend_handles, loc='best', fontsize=7)
