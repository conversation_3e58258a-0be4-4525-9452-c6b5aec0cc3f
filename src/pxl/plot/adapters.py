#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from typing import Any, Dict, Optional
import numpy as np

from ..core.interfaces import <PERSON>lot<PERSON><PERSON><PERSON>, PlotData, DataObserver
from ..util.logging_config import get_logger
from .sbc import SingleBarChart
from .dt import DynamicTable
from .slc import ScatterLineChart
from .cie1937 import CIE1931ChromaticityDiagram
from .trace_diagram import TraceDiagram


class SingleBarChartRenderer(PlotRenderer, DataObserver):
    """Adapter for SingleBarChart to implement PlotRenderer interface."""
    
    def __init__(self, chart: SingleBarChart):
        self._chart = chart
        self._logger = get_logger(__name__)
    
    def render(self, data: PlotData) -> None:
        """Render data on the bar chart."""
        if data.data_type == 'delta_e':
            chart_data = data.data.get('values', [])
            for i, value in enumerate(chart_data):
                self._chart.add_value(i, value, redraw=False)
            self._chart._ax.figure.canvas.draw_idle()
    
    def clear(self) -> None:
        """Clear the bar chart."""
        self._chart._ax.clear()
    
    def redraw(self) -> None:
        """Redraw the bar chart."""
        self._chart._ax.figure.canvas.draw_idle()
    
    def update(self, data: PlotData) -> None:
        """Update method for DataObserver interface."""
        self.render(data)


class DynamicTableRenderer(PlotRenderer, DataObserver):
    """Adapter for DynamicTable to implement PlotRenderer interface."""
    
    def __init__(self, table: DynamicTable):
        self._table = table
        self._logger = get_logger(__name__)
    
    def render(self, data: PlotData) -> None:
        """Render data on the dynamic table."""
        if data.data_type == 'table':
            row_data = data.data.get('row', [])
            if row_data:
                self._table.add_external_data(row_data, redraw=False)
    
    def clear(self) -> None:
        """Clear the table."""
        # DynamicTable doesn't have a clear method, so we recreate it
        pass
    
    def redraw(self) -> None:
        """Redraw the table."""
        self._table._ax.figure.canvas.draw_idle()
    
    def update(self, data: PlotData) -> None:
        """Update method for DataObserver interface."""
        self.render(data)


class ScatterLineChartRenderer(PlotRenderer, DataObserver):
    """Adapter for ScatterLineChart to implement PlotRenderer interface."""
    
    def __init__(self, chart: ScatterLineChart):
        self._chart = chart
        self._logger = get_logger(__name__)
    
    def render(self, data: PlotData) -> None:
        """Render data on the scatter line chart."""
        if data.data_type == 'scatter_line':
            x_value = data.data.get('x', 0)
            a_value = data.data.get('a', 0)
            b_value = data.data.get('b', 0)
            self._chart.add_value_pair(x_value, a_value, b_value, redraw=False)
    
    def clear(self) -> None:
        """Clear the scatter line chart."""
        self._chart._ax.clear()
    
    def redraw(self) -> None:
        """Redraw the scatter line chart."""
        self._chart._ax.figure.canvas.draw_idle()
    
    def update(self, data: PlotData) -> None:
        """Update method for DataObserver interface."""
        self.render(data)


class CIEDiagramRenderer(PlotRenderer, DataObserver):
    """Adapter for CIE1931ChromaticityDiagram to implement PlotRenderer interface."""
    
    def __init__(self, diagram: CIE1931ChromaticityDiagram):
        self._diagram = diagram
        self._logger = get_logger(__name__)
    
    def render(self, data: PlotData) -> None:
        """Render data on the CIE diagram."""
        if data.data_type == 'chromaticity':
            target_xyz = data.data.get('target_xyz')
            measured_xyz = data.data.get('measured_xyz')
            redraw = data.data.get('redraw', True)
            
            if target_xyz is not None:
                self._diagram.add_a_color(target_xyz, redraw=False)
            if measured_xyz is not None:
                self._diagram.add_b_color(measured_xyz, redraw=False)
            
            if redraw:
                self._diagram._ax.figure.canvas.draw_idle()
    
    def clear(self) -> None:
        """Clear the CIE diagram."""
        self._diagram._ax.clear()
        self._diagram._plot_cie_diagram_outline()
    
    def redraw(self) -> None:
        """Redraw the CIE diagram."""
        self._diagram._ax.figure.canvas.draw_idle()
    
    def update(self, data: PlotData) -> None:
        """Update method for DataObserver interface."""
        self.render(data)


class TraceDiagramRenderer(PlotRenderer, DataObserver):
    """Adapter for TraceDiagram to implement PlotRenderer interface."""
    
    def __init__(self, diagram: TraceDiagram):
        self._diagram = diagram
        self._logger = get_logger(__name__)
    
    def render(self, data: PlotData) -> None:
        """Render data on the trace diagram."""
        if data.data_type == 'trace':
            if data.data.get('final', False):
                # Final plot
                md_sections = data.data.get('md_sections')
                measurement_data = data.data.get('measurement_data')
                upper_tar = data.data.get('upper_tar')
                lower_tar = data.data.get('lower_tar')
                result_color = data.data.get('result_color', 'black')
                
                if all(x is not None for x in [md_sections, measurement_data, upper_tar, lower_tar]):
                    self._diagram.final_plot(md_sections, measurement_data, upper_tar, lower_tar, result_color)
            else:
                # Regular trace plot
                md_sections = data.data.get('md_sections')
                measurement_data = data.data.get('measurement_data')
                result_color = data.data.get('result_color', 'black')
                
                if md_sections is not None and measurement_data is not None:
                    self._diagram.trace_plot(md_sections, measurement_data, result_color)
    
    def clear(self) -> None:
        """Clear the trace diagram."""
        self._diagram.ax.clear()
        self._diagram.figure_assemble()
    
    def redraw(self) -> None:
        """Redraw the trace diagram."""
        self._diagram.ax.figure.canvas.draw_idle()
    
    def update(self, data: PlotData) -> None:
        """Update method for DataObserver interface."""
        self.render(data)
