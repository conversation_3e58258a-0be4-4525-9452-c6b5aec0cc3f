#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Jun 12, 2024
"""

from typing import Iterable

import matplotlib.pyplot as plt
from matplotlib.artist import Artist


class ScatterLineChart:
    def __init__(self, ax, title="Scatter Line Chart (A/B)", style_line_a=None, style_line_b=None, axis_style=None):
        """Initialize the ScatterLineChart with a matplotlib axis and optional styles.

        Args:
            ax (matplotlib.axes.Axes): The matplotlib axis object for drawing.
            title (str): The title of the chart. Defaults to "Scatter Line Chart (A/B)".
            style_line_a (dict): Custom style options for line A. Defaults to None.
            style_line_b (dict): Custom style options for line B. Defaults to None.
            axis_style (dict): Custom style options for the axis. Defaults to None.

        Returns:
            None
        """
        self._ax = ax

        # Define default styles for plot lines
        self._default_plot_style = {
            'line_a': {
                'marker': 's', 'color': 'blue', 'markersize': 5,
                'markerfacecolor': 'none', 'linewidth': 0.8, 'label': 'A'
            },
            'line_b': {
                'marker': 'o', 'color': 'red', 'markersize': 5,
                'markerfacecolor': 'none', 'linewidth': 0.8, 'label': 'B'
            }
        }

        # Define default styles for axis labels
        self._default_axis_style = {
            'xlabel': 'Index',
            'ylabel': 'Value'
        }

        # Update plot styles with any custom styles provided
        if style_line_a:
            self._default_plot_style['line_a'].update(style_line_a)
        if style_line_b:
            self._default_plot_style['line_b'].update(style_line_b)

        # Update axis styles with any custom styles provided
        if axis_style:
            self._default_axis_style.update(axis_style)

        # Initialize the lines using the plot styles
        self._line_a, = ax.plot([], [], **self._default_plot_style['line_a'])
        self._line_b, = ax.plot([], [], **self._default_plot_style['line_b'])

        # Apply axis styles
        self._ax.set_xlabel(self._default_axis_style['xlabel'], fontsize=7)
        self._ax.set_ylabel(self._default_axis_style['ylabel'], fontsize=7)

        # Set up grid, legend
        self._ax.grid(True, which='both', linestyle='--', linewidth=0.5)
        self._ax.legend(fontsize=7)

        # Customize tick labels font size
        self._ax.tick_params(axis='both', which='major', labelsize=6)

        # set title
        self._ax.set_title(title)

    def add_value_pair(self, x_value, a_value, b_value, redraw: bool = True) -> Iterable[Artist]:
        """Add a new pair of values to both Line A and Line B.

        Args:
            x_value (float): The x-coordinate value for the new points.
            a_value (float): The y-coordinate value for line A.
            b_value (float): The y-coordinate value for line B.
            redraw (bool): Whether to redraw the plot. Defaults to True.

        Returns:
            Iterable[Artist]: The updated line objects for Line A and Line B.
        """

        # Update data for line A
        new_a_data_x = list(self._line_a.get_xdata()) + [x_value]
        new_a_data_y = list(self._line_a.get_ydata()) + [a_value]
        self._line_a.set_data(new_a_data_x, new_a_data_y)

        # Update data for line B
        new_b_data_x = list(self._line_b.get_xdata()) + [x_value]
        new_b_data_y = list(self._line_b.get_ydata()) + [b_value]
        self._line_b.set_data(new_b_data_x, new_b_data_y)

        # redraw the graph
        if redraw:
            # Adjust plot limits and redraw
            self._ax.relim()
            self._ax.autoscale_view()
            self._ax.figure.canvas.draw_idle()

        return self._line_a, self._line_b,


if __name__ == "__main__":
    import time

    # test code
    fig, ax = plt.subplots(figsize=(10, 3))
    plotter = ScatterLineChart(ax)

    # Simulate real-time data update
    for i in range(1, 38):
        # Replace these with actual dynamic data updates
        target_value = 5 if i % 5 == 0 else 1
        measurement_value = 4 if i % 7 == 0 else 2

        # Update the plot with the new data point
        start_time = time.time()
        plotter.add_value_pair(i, target_value, measurement_value)
        elapsed_time = time.time() - start_time
        print(f"sleep time: {elapsed_time}")
        plt.pause(0.1)  # Pause briefly to show updates

    plt.show()
