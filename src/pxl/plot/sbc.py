#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Jun 12, 2024
"""

from typing import Iterable

import matplotlib.patches as mpatches
import matplotlib.pyplot as plt
from matplotlib.artist import Artist


class SingleBarChart:
    def __init__(self, ax, title="Bar Chart (single)",
                 style_bar=None, axis_style=None, font_properties=None,
                 thresholds=None):
        """Initialize a SingleBarChart object with a matplotlib axis.

        Args:
            ax (matplotlib.axes.Axes): The axis object where the bar chart will be drawn.
            title (str): The title of the chart. Defaults to "Bar Chart (single)".
            style_bar (dict): Custom style options for the bars. Defaults to None.
            axis_style (dict): Custom style options for the axis labels. Defaults to None.
            font_properties (dict): Custom font properties for the text. Defaults to None.
            thresholds (list): List of (threshold_value, color) tuples. Defaults to None.

        Returns:
            None
        """
        self._ax = ax

        # Define default styles for plot lines
        self._default_plot_style = {
            'color': 'blue', 'linewidth': 0.8, 'label': 'A'
        }

        # Define default styles for axis labels
        self._default_axis_style = {
            'xlabel': 'Index',
            'ylabel': 'Value'
        }

        # Define font properties
        self._default_font_properties = {
            'family': 'serif',
            'color': 'black',
            'weight': 'normal',
            'size': 16,
        }

        # Update plot styles with any custom styles provided
        if style_bar:
            self._default_plot_style.update(style_bar)

        # Update axis styles with any custom styles provided
        if axis_style:
            self._default_axis_style.update(axis_style)

        # Update font properties
        if font_properties:
            self._default_font_properties.update(font_properties)

        # Set thresholds and associated colors
        # Example format: [(threshold1, 'color1'), (threshold2, 'color2'), ...]
        self._default_thresholds = thresholds or [(2, 'green'), (5, 'orange'), (float('inf'), 'red')]

        # Create the bar chart
        self._bars = None
        self._texts = []
        self._legend_handles = {}  # Dictionary to store color and corresponding legend patch

        # Set up grid, legend, and axis labels
        self._ax.grid(True, which='both', linestyle='--', linewidth=0.5)
        self._ax.set_xlabel(self._default_axis_style['xlabel'], fontsize=7)
        self._ax.set_ylabel(self._default_axis_style['ylabel'], fontsize=7)

        # Customize tick labels font size
        self._ax.tick_params(axis='both', which='major', labelsize=6)

        # set title
        self._ax.set_title(title)

    def add_value(self, x_value, y_value, redraw: bool = True) -> Iterable[Artist]:
        """Add a single value to the bar chart at the specified x position.

        Args:
            x_value (float): The x position where the bar will be added.
            y_value (float): The height of the bar to be added.
            redraw (bool): Whether to redraw the chart. Defaults to True.

        Returns:
            Iterable[Artist]: The updated axis object.
        """
        return self._update_plot(x_value, y_value, redraw)

    def _get_color_based_on_threshold(self, value):
        for threshold, color in self._default_thresholds:
            if value <= threshold:
                return color
        return 'gray'  # Default color if no threshold matches

    def _update_legend(self):
        # Clear existing legend handles and create new ones based on current thresholds
        self._legend_handles = {}
        for threshold, color in self._default_thresholds:
            if threshold == float('inf'):
                label = f"> {self._default_thresholds[-2][0]}"  # Assuming there is a value before 'inf'
            else:
                label = f"≤ {threshold}"
            self._legend_handles[color] = mpatches.Patch(color=color, label=label)
        self._ax.legend(handles=list(self._legend_handles.values()), fontsize=7, loc='upper right',
                        bbox_to_anchor=(1, 1))

    def _update_plot(self, x_value, y_value, redraw) -> Iterable[Artist]:
        color = self._get_color_based_on_threshold(y_value)

        # Ensure the color from the thresholds takes precedence
        bar_style = self._default_plot_style.copy()
        bar_style['color'] = color

        # If this is the first bar, create the bar container
        if self._bars is None:
            self._bars = self._ax.bar([x_value], [y_value], **bar_style)
            # Set up legend
            self._update_legend()
        else:
            # If x_value is beyond the current number of bars, append a new bar
            if x_value >= len(self._bars.patches):
                # Extend the bar container by plotting a new bar and appending it
                new_bar = self._ax.bar([x_value], [y_value], **bar_style)
                self._bars.patches.extend(new_bar.patches)
                # Add a new text label for the new bar
                text = self._ax.text(x_value, y_value, str(y_value),
                                     ha='center', va='bottom', color=color, size=6)
                self._texts.append(text)
            else:
                # Update the height and color of the existing bar
                self._bars.patches[x_value].set_height(y_value)
                self._bars.patches[x_value].set_color(color)
                # Update the text label
                if x_value < len(self._texts):
                    self._texts[x_value].set_position((x_value, y_value))
                    self._texts[x_value].set_text(str(y_value))

        # Redraw the plot with the new changes
        if redraw:
            self._ax.figure.canvas.draw_idle()

        return self._ax,


if __name__ == "__main__":
    # test code
    import time
    import matplotlib.gridspec as gridspec

    # Create a figure with a single subplot for the chromaticity diagram
    fig = plt.figure(figsize=(12, 6))

    # Define a 2x2 grid specification
    gs = gridspec.GridSpec(3, 2, figure=fig)

    # Create subplots

    # ax_cie1937 = fig.add_subplot(gs[:, 0])  # TODO: A subplot in the first column spanning all rows
    ax_Y = fig.add_subplot(gs[0, 1])  # A subplot in the second column, first row
    ax_DE = fig.add_subplot(gs[1, 1])  # A subplot in the second column, second row
    ax_cie1937 = fig.add_subplot(gs[2, 1])  # A subplot in the second column, third row
    sbc = SingleBarChart(
        ax_DE,
        title="DeltaE 2000 (Measurement vs. Target)",
        axis_style={
            'xlabel': 'Pattern',
            'ylabel': 'DeltaE'
        },
        thresholds=[
            (2.5, (0.5, 0.8, 0.5)),  # Less saturated green
            (10, (1.0, 0.7, 0.4)),  # Less saturated orange
            (float('inf'), (0.9, 0.5, 0.5))  # Less saturated red
        ]
    )

    # Simulate adding data to the bar chart
    for i in range(57):
        start_time = time.time()
        sbc.add_value(i, i % 10 + 1)
        elapsed_time = time.time() - start_time
        print(f"sleep time: {elapsed_time}")

    plt.show()
