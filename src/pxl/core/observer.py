#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from typing import List, Dict, Set
from .interfaces import DataObserver, PlotData
from ..util.logging_config import get_logger


class DataPublisher:
    """Publisher for the observer pattern. Manages data observers and notifies them of updates."""
    
    def __init__(self):
        self._observers: Dict[str, Set[DataObserver]] = {}
        self._logger = get_logger(__name__)
    
    def subscribe(self, data_type: str, observer: DataObserver) -> None:
        """Subscribe an observer to a specific data type.
        
        Args:
            data_type: The type of data to observe
            observer: The observer to subscribe
        """
        if data_type not in self._observers:
            self._observers[data_type] = set()
        
        self._observers[data_type].add(observer)
        self._logger.debug(f"Observer {observer.__class__.__name__} subscribed to {data_type}")
    
    def unsubscribe(self, data_type: str, observer: DataObserver) -> None:
        """Unsubscribe an observer from a specific data type.
        
        Args:
            data_type: The type of data to stop observing
            observer: The observer to unsubscribe
        """
        if data_type in self._observers and observer in self._observers[data_type]:
            self._observers[data_type].remove(observer)
            self._logger.debug(f"Observer {observer.__class__.__name__} unsubscribed from {data_type}")
    
    def notify(self, data: PlotData) -> None:
        """Notify all observers of a specific data type.
        
        Args:
            data: The plot data to notify observers with
        """
        data_type = data.data_type
        if data_type in self._observers:
            for observer in self._observers[data_type]:
                try:
                    observer.update(data)
                except Exception as e:
                    self._logger.error(f"Error notifying observer {observer.__class__.__name__}: {e}", exc_info=True)
    
    def notify_all(self, data: PlotData) -> None:
        """Notify all observers regardless of data type.
        
        Args:
            data: The plot data to notify observers with
        """
        for observers in self._observers.values():
            for observer in observers:
                try:
                    observer.update(data)
                except Exception as e:
                    self._logger.error(f"Error notifying observer {observer.__class__.__name__}: {e}", exc_info=True)
    
    def get_observer_count(self, data_type: str = None) -> int:
        """Get the number of observers for a data type or total.
        
        Args:
            data_type: The data type to count observers for, or None for total
            
        Returns:
            Number of observers
        """
        if data_type:
            return len(self._observers.get(data_type, set()))
        else:
            return sum(len(observers) for observers in self._observers.values())
    
    def clear_observers(self, data_type: str = None) -> None:
        """Clear observers for a data type or all observers.
        
        Args:
            data_type: The data type to clear observers for, or None for all
        """
        if data_type:
            if data_type in self._observers:
                self._observers[data_type].clear()
        else:
            self._observers.clear()
        
        self._logger.debug(f"Cleared observers for {data_type or 'all data types'}")
