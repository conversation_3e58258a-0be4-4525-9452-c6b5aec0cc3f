#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from typing import List, Dict, Optional
import matplotlib.pyplot as plt
from matplotlib.figure import Figure

from .interfaces import PlotRenderer, PlotData, DataObserver
from .observer import DataPublisher
from ..util.logging_config import get_logger


class PlotManager(DataObserver):
    """Manages multiple plot renderers and coordinates their updates."""
    
    def __init__(self, figure: Optional[Figure] = None):
        self._renderers: Dict[str, PlotRenderer] = {}
        self._figure = figure
        self._logger = get_logger(__name__)
        self._publisher = DataPublisher()
    
    def add_renderer(self, name: str, renderer: PlotRenderer) -> None:
        """Add a plot renderer.
        
        Args:
            name: Unique name for the renderer
            renderer: The plot renderer to add
        """
        self._renderers[name] = renderer
        self._logger.debug(f"Added renderer: {name}")
    
    def remove_renderer(self, name: str) -> None:
        """Remove a plot renderer.
        
        Args:
            name: Name of the renderer to remove
        """
        if name in self._renderers:
            del self._renderers[name]
            self._logger.debug(f"Removed renderer: {name}")
    
    def get_renderer(self, name: str) -> Optional[PlotRenderer]:
        """Get a plot renderer by name.
        
        Args:
            name: Name of the renderer
            
        Returns:
            The plot renderer or None if not found
        """
        return self._renderers.get(name)
    
    def update(self, data: PlotData) -> None:
        """Update plots based on data type.
        
        Args:
            data: The plot data to update with
        """
        # Route data to appropriate renderers based on data type
        if data.data_type in self._renderers:
            try:
                self._renderers[data.data_type].render(data)
            except Exception as e:
                self._logger.error(f"Error rendering {data.data_type}: {e}", exc_info=True)
        
        # Also notify any subscribers
        self._publisher.notify(data)
    
    def render_all(self, data: PlotData) -> None:
        """Render data on all renderers.
        
        Args:
            data: The plot data to render
        """
        for name, renderer in self._renderers.items():
            try:
                renderer.render(data)
            except Exception as e:
                self._logger.error(f"Error rendering on {name}: {e}", exc_info=True)
    
    def clear_all(self) -> None:
        """Clear all renderers."""
        for renderer in self._renderers.values():
            try:
                renderer.clear()
            except Exception as e:
                self._logger.error(f"Error clearing renderer: {e}", exc_info=True)
    
    def redraw_all(self) -> None:
        """Redraw all renderers."""
        for renderer in self._renderers.values():
            try:
                renderer.redraw()
            except Exception as e:
                self._logger.error(f"Error redrawing renderer: {e}", exc_info=True)
        
        # Redraw the figure if available
        if self._figure:
            try:
                self._figure.canvas.draw_idle()
                self._figure.canvas.flush_events()
            except Exception as e:
                self._logger.error(f"Error redrawing figure: {e}", exc_info=True)
    
    def subscribe_observer(self, data_type: str, observer: DataObserver) -> None:
        """Subscribe an observer to data updates.
        
        Args:
            data_type: The type of data to observe
            observer: The observer to subscribe
        """
        self._publisher.subscribe(data_type, observer)
    
    def unsubscribe_observer(self, data_type: str, observer: DataObserver) -> None:
        """Unsubscribe an observer from data updates.
        
        Args:
            data_type: The type of data to stop observing
            observer: The observer to unsubscribe
        """
        self._publisher.unsubscribe(data_type, observer)
