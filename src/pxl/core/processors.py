#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from typing import Tuple, Optional, List
import numpy as np

from .interfaces import DataProcessor, PlotData, CalculatorEngine
from ..metrics.de2k import DE2000
from ..clrspc.xyz import XYZ2xyz, visualize_XYZ
from ..util.logging_config import get_logger


class DeltaEDataProcessor(DataProcessor):
    """Data processor for Delta E calculations."""
    
    def __init__(self, scale_multiplier: float = 1.0, xyz_ref_w: Optional[np.ndarray] = None):
        self._scale_multiplier = scale_multiplier
        self._xyz_ref_w = xyz_ref_w
        self._logger = get_logger(__name__)
    
    def set_scale_multiplier(self, multiplier: float) -> None:
        """Set the scale multiplier."""
        self._scale_multiplier = multiplier
    
    def set_reference_white(self, xyz_ref_w: np.ndarray) -> None:
        """Set the reference white."""
        self._xyz_ref_w = xyz_ref_w
    
    def process_data(self, pattern_index: int, target_xyz: np.ndarray, 
                    measured_xyz: np.ndarray, **kwargs) -> List[PlotData]:
        """Process Delta E data and return plot data for different components.
        
        Args:
            pattern_index: Pattern index
            target_xyz: Target XYZ values
            measured_xyz: Measured XYZ values
            
        Returns:
            List of plot data for different components
        """
        plot_data_list = []
        
        # Apply scale factor to target_XYZ
        xyz_t = target_xyz * self._scale_multiplier
        xyz_m = measured_xyz
        
        # Calculate Delta E
        if self._xyz_ref_w is not None:
            de_value, _, __, ___ = DE2000(
                XYZ1=np.array(xyz_t),
                XYZ2=np.array(xyz_m),
                XYZWP=np.array(self._xyz_ref_w)
            )
        else:
            de_value = 0.0
            self._logger.warning("Reference white not set, Delta E calculation skipped")
        
        # Create plot data for Delta E chart
        delta_e_data = PlotData(
            data_type='delta_e',
            data={
                'pattern_index': pattern_index,
                'value': de_value,
                'values': [de_value]  # For compatibility with existing chart
            }
        )
        plot_data_list.append(delta_e_data)
        
        # Create plot data for chromaticity diagram
        chromaticity_data = PlotData(
            data_type='chromaticity',
            data={
                'target_xyz': xyz_t,
                'measured_xyz': xyz_m,
                'redraw': kwargs.get('redraw', True)
            }
        )
        plot_data_list.append(chromaticity_data)
        
        # Create plot data for Y scatter line chart
        y_scatter_data = PlotData(
            data_type='scatter_line',
            data={
                'x': pattern_index,
                'a': target_xyz[1],  # Y component
                'b': measured_xyz[1]  # Y component
            }
        )
        plot_data_list.append(y_scatter_data)
        
        # Create plot data for table
        table_data = PlotData(
            data_type='table',
            data={
                'row': [
                    pattern_index,
                    f"{target_xyz[0]:.4f}", f"{target_xyz[1]:.4f}", f"{target_xyz[2]:.4f}",
                    f"{measured_xyz[0]:.4f}", f"{measured_xyz[1]:.4f}", f"{measured_xyz[2]:.4f}",
                    f"{de_value:.4f}"
                ]
            }
        )
        plot_data_list.append(table_data)
        
        return plot_data_list


class GDMBDataProcessor(DataProcessor):
    """Data processor for GDMB calculations."""
    
    def __init__(self, tmax: float, measurement_step: int = 80, delta_tolerance: float = 0.1):
        self._tmax = tmax
        self._measurement_step = measurement_step
        self._delta_tolerance = delta_tolerance
        self._logger = get_logger(__name__)
    
    def process_data(self, pattern_index: int, target_y: float, 
                    measurement_y: float, **kwargs) -> List[PlotData]:
        """Process GDMB data and return plot data for different components.
        
        Args:
            pattern_index: Pattern index
            target_y: Target Y value
            measurement_y: Measured Y value
            
        Returns:
            List of plot data for different components
        """
        plot_data_list = []
        
        # Create plot data for table
        table_data = PlotData(
            data_type='table',
            data={
                'row': [pattern_index, target_y, measurement_y]
            }
        )
        plot_data_list.append(table_data)
        
        # Create plot data for trace diagram (if we have accumulated data)
        x_points = kwargs.get('x_points', [])
        y_points = kwargs.get('y_points', [])
        
        if x_points and y_points:
            trace_data = PlotData(
                data_type='trace',
                data={
                    'md_sections': np.asarray(x_points),
                    'measurement_data': np.asarray(y_points),
                    'final': False
                }
            )
            plot_data_list.append(trace_data)
        
        return plot_data_list
    
    def process_final_data(self, x_points: List[float], scaled_data: List[float],
                          upper_tar: List[float], lower_tar: List[float],
                          result_color: str = 'black') -> PlotData:
        """Process final GDMB data for final plot.
        
        Args:
            x_points: X axis points
            scaled_data: Scaled measurement data
            upper_tar: Upper target values
            lower_tar: Lower target values
            result_color: Color for the result
            
        Returns:
            Plot data for final trace diagram
        """
        return PlotData(
            data_type='trace',
            data={
                'md_sections': np.asarray(x_points),
                'measurement_data': np.asarray(scaled_data),
                'upper_tar': np.asarray(upper_tar),
                'lower_tar': np.asarray(lower_tar),
                'result_color': result_color,
                'final': True
            }
        )
