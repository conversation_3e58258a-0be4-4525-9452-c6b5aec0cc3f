#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union
import numpy as np


@dataclass
class PlotData:
    """Data transfer object for plot updates."""
    data_type: str  # 'delta_e', 'chromaticity', 'table', 'trace', etc.
    data: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None


class DataObserver(ABC):
    """Abstract interface for data observers."""
    
    @abstractmethod
    def update(self, data: PlotData) -> None:
        """Update the observer with new data.
        
        Args:
            data: The plot data to update with
        """
        pass


class PlotRenderer(ABC):
    """Abstract interface for plot renderers."""
    
    @abstractmethod
    def render(self, data: PlotData) -> None:
        """Render the plot with the given data.
        
        Args:
            data: The plot data to render
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Clear the plot."""
        pass
    
    @abstractmethod
    def redraw(self) -> None:
        """Redraw the plot."""
        pass


class DataProcessor(ABC):
    """Abstract interface for data processors."""
    
    @abstractmethod
    def process_data(self, *args, **kwargs) -> PlotData:
        """Process input data and return plot data.
        
        Returns:
            Processed plot data
        """
        pass


class CalculatorEngine(ABC):
    """Abstract interface for calculation engines."""
    
    @abstractmethod
    def calculate(self, *args, **kwargs) -> Any:
        """Perform calculations.
        
        Returns:
            Calculation results
        """
        pass
    
    @abstractmethod
    def get_report(self) -> Any:
        """Get calculation report.
        
        Returns:
            Calculation report
        """
        pass
