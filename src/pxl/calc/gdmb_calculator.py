#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from typing import Optional
import numpy as np
import matplotlib.gridspec as gridspec

from .base import BaseCalculator, CalculatorConfig, CalculatorState, CalculatorFactory
from .data_manager import DataManager
from .computation import GDMBComputation
from .state_manager import StateManager
from .figure_manager import FigureManager
from ..plot.dt import DynamicTable
from ..plot.trace_diagram import TraceDiagram
from ..util.decorators import ensure_fixed_interval
from ..util.pusher import BytesIOPusher
from ..util.exceptions import ConfigurationError, CalculationError


class GDMBCalculatorConfig(CalculatorConfig):
    """Configuration for GDMB calculator."""
    
    def __init__(self, 
                 tmax: float,
                 debug: bool = False,
                 scale_tolerance: float = 0.1,
                 delta_tolerance: float = 0.1,
                 **kwargs):
        """Initialize GDMB calculator configuration.
        
        Args:
            tmax: Maximum target value
            debug: Enable debug mode
            scale_tolerance: Scale tolerance
            delta_tolerance: Delta tolerance
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        
        if tmax <= 0:
            raise ConfigurationError("tmax must be positive")
        
        self.tmax = tmax
        self.debug = debug
        self.scale_tolerance = scale_tolerance
        self.delta_tolerance = delta_tolerance


class GDMBCalculatorRefactored(BaseCalculator):
    """
    Refactored GDMB Calculator with improved separation of concerns.
    
    This class demonstrates the single responsibility principle by delegating
    different concerns to specialized components:
    - DataManager: handles data validation and buffering
    - GDMBComputation: performs GDMB calculations
    - StateManager: manages calculator state and timing
    - FigureManager: handles matplotlib figure operations
    """
    
    def __init__(self, config: GDMBCalculatorConfig):
        """Initialize the refactored GDMB Calculator.
        
        Args:
            config: GDMB calculator configuration
        """
        super().__init__(config)
        
        # Store specific config
        self._gdmb_config = config
        
        # Initialize components
        self._data_manager = DataManager()
        self._computation = GDMBComputation(
            tmax=config.tmax,
            scale_tolerance=config.scale_tolerance,
            delta_tolerance=config.delta_tolerance
        )
        self._state_manager = StateManager()
        self._figure_manager = FigureManager(
            figure_size=config.figure_size,
            show_figure=config.show_figure,
            dynamic=config.dynamic,
            bytesio_pusher=config.bytesio_pusher,
            logo=config.logo
        )
        
        # Plot components (will be initialized in initialize())
        self._trace_diagram = None
        self._dynamic_data = None
        
        self._logger.info("GDMB Calculator (Refactored) initialized")
    
    def initialize(self) -> None:
        """Initialize calculator components."""
        self._logger.info("Initializing GDMB Calculator components...")
        
        # Create figure and layout
        self._figure_manager.create_figure()
        gs = self._figure_manager.create_gridspec_layout(2, 1)
        
        # Create axes
        ax_trace = self._figure_manager.add_axis('trace', gs[0, 0])
        ax_table = self._figure_manager.add_axis('table', gs[1, 0])
        
        # Initialize plot components
        self._init_plot_components(ax_trace, ax_table)
        
        # Add watermark
        self._figure_manager.add_watermark()
        
        self.set_state(CalculatorState.INITIALIZED)
        self._logger.info("GDMB Calculator initialization completed")
    
    def _init_plot_components(self, ax_trace, ax_table) -> None:
        """Initialize plot components."""
        # Initialize the trace diagram
        self._trace_diagram = TraceDiagram(ax_trace, self._gdmb_config.tmax)

        # Initialize the dynamic table
        self._dynamic_data = DynamicTable(
            ax_table,
            columns=['Index', 'Target Y', 'Measured Y'],
            column_width=0.33,
        )
    
    def start(self) -> None:
        """Start the calculator."""
        if self._state != CalculatorState.INITIALIZED:
            raise RuntimeError("Calculator must be initialized before starting")
        
        self.set_state(CalculatorState.RUNNING)
        
        if self._dynamic:
            self._figure_manager.start_dynamic_updates(1.0, self._update_plot)
        
        self._logger.info("GDMB Calculator started")
    
    def stop(self) -> None:
        """Stop the calculator."""
        self.set_state(CalculatorState.COMPLETED)
        self._state_manager.set_flag('stop_draw', True)
        self._figure_manager.stop_dynamic_updates()
        self._logger.info("GDMB Calculator stopped")
    
    def reset(self) -> None:
        """Reset the calculator to initial state."""
        self.stop()
        self._data_manager.clear_history()
        self._computation.reset()
        self._state_manager.reset()
        self.set_state(CalculatorState.INITIALIZED)
        self._logger.info("GDMB Calculator reset")
    
    @ensure_fixed_interval(1.0)
    def receive_data(self, pattern_index: int, target_y: float, measured_y: float,
                     redraw: bool = True, interval: float = 1.0) -> bool:
        """Receive and process measurement data.
        
        Args:
            pattern_index: Pattern index
            target_y: Target Y value
            measured_y: Measured Y value
            redraw: Whether to redraw plots
            interval: Update interval
            
        Returns:
            True if max flag is reached
        """
        try:
            data = (pattern_index, target_y, measured_y, redraw)
            self._data_manager.buffer.put(data, block=False)
            
            # Perform computation immediately for return value
            max_flag = self._computation.compute(target_y, measured_y)
            return max_flag
            
        except Exception as e:
            self._logger.warning(f"Failed to receive data: {e}")
            return False
    
    def batch_receive_data(self, target_y_list: np.ndarray, measured_y_list: np.ndarray):
        """Receive batch data for processing.
        
        Args:
            target_y_list: Array of target Y values
            measured_y_list: Array of measured Y values
        """
        if len(target_y_list) != len(measured_y_list):
            raise CalculationError("Target and measured data lists must have the same length")
        
        for i, (target_y, measured_y) in enumerate(zip(target_y_list, measured_y_list)):
            self.receive_data(i, float(target_y), float(measured_y), redraw=False)
    
    def _update_plot(self, frame=None):
        """Update plots with new data."""
        if self._state_manager.get_flag('stop_draw'):
            return
        
        try:
            # Get data from buffer
            data = self._data_manager.buffer.get_nowait()
            pattern_index, target_y, measured_y, redraw = data
            
            # Update plots
            self._update_plot_components(pattern_index, target_y, measured_y)
            
            # Store data point
            self._data_manager.add_data_point(pattern_index, {
                'target_y': target_y,
                'measured_y': measured_y
            })
            
            # Redraw if needed
            if redraw:
                self._figure_manager.redraw()
                self._figure_manager.push_figure_data()
            
        except Exception as e:
            if "Empty" not in str(e):  # Don't log empty queue errors
                self._logger.debug(f"Update plot error: {e}")
    
    def _update_plot_components(self, pattern_index: int, target_y: float, measured_y: float):
        """Update individual plot components."""
        # Update table
        table_row = [pattern_index, f"{target_y:.4f}", f"{measured_y:.4f}"]
        self._dynamic_data.add_external_data(table_row, redraw=False)
        
        # Update trace diagram if we have enough data
        measurement_data = self._computation.measurement_data
        target_data = self._computation.target_data
        
        if len(measurement_data) > 1:
            x_points = list(range(len(measurement_data)))
            self._trace_diagram.trace_plot(
                np.array(x_points), 
                np.array(measurement_data), 
                'blue'
            )
    
    def scale_factor(self) -> float:
        """Calculate and return scale factor.
        
        Returns:
            Scale factor value
        """
        return self._computation.calculate_scale_factor()
    
    def result_table_fill(self) -> dict:
        """Fill result table with final data.
        
        Returns:
            Dictionary with result data
        """
        scale_factor = self.scale_factor()
        
        return {
            'scale_factor': scale_factor,
            'tmax': self._computation.tmax,
            'mmax': self._computation.mmax,
            'measurement_count': len(self._computation.measurement_data)
        }
    
    def done(self):
        """Signal completion of data processing."""
        # Calculate final scale factor
        scale_factor = self.scale_factor()
        
        # Create final plot
        if self._trace_diagram and len(self._computation.measurement_data) > 0:
            x_points = list(range(len(self._computation.measurement_data)))
            scaled_data = self._computation.scaled_data
            target_data = self._computation.target_data
            
            # Create upper and lower target bounds (simplified)
            upper_tar = [t * 1.1 for t in target_data]  # 10% above target
            lower_tar = [t * 0.9 for t in target_data]  # 10% below target
            
            self._trace_diagram.final_plot(
                np.array(x_points),
                np.array(scaled_data),
                np.array(upper_tar),
                np.array(lower_tar),
                'green'
            )
        
        self.stop()
        self._logger.info("GDMB calculation completed")
    
    def plot_figure(self, save_figure: Optional[str] = None):
        """Plot the figure with all components.
        
        Args:
            save_figure: Optional filename to save the figure
        """
        # Start the calculator if not already started
        if self._state.value == 'initialized':
            self.start()
        
        if not self._dynamic:
            # Process all data in buffer for static mode
            while not self._data_manager.buffer.empty():
                self._update_plot()
        
        self._figure_manager.tight_layout()
        
        if save_figure:
            self._figure_manager.save_figure(save_figure)
        
        self._figure_manager.show()
    
    def get_report(self) -> dict:
        """Get calculation report.
        
        Returns:
            GDMB calculation report
        """
        return {
            'tmax': self._computation.tmax,
            'mmax': self._computation.mmax,
            'scale_factor': self._computation.scale_ratio,
            'measurement_count': len(self._computation.measurement_data),
            'target_count': len(self._computation.target_data)
        }
    
    def cleanup(self) -> None:
        """Cleanup calculator resources."""
        super().cleanup()
        self._figure_manager.cleanup()
        self._state_manager.cleanup()


# Register the refactored calculator
CalculatorFactory.register('gdmb_refactored', GDMBCalculatorRefactored)
