#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from typing import Tuple, Optional, Any
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.figure import Figure
from matplotlib import get_backend, use
from io import BytesIO

from ..util.logging_config import get_logger
from ..util.watermark import add_watermark2
from ..util.pusher import BytesIOPusher


class FigureManager:
    """Manages matplotlib figures and plotting operations."""
    
    def __init__(self, 
                 figure_size: Tuple[float, float] = (12.0, 7.0),
                 show_figure: bool = True,
                 dynamic: bool = True,
                 bytesio_pusher: Optional[BytesIOPusher] = None,
                 logo: Optional[Any] = None):
        """Initialize figure manager.
        
        Args:
            figure_size: Size of the figure
            show_figure: Whether to show the figure
            dynamic: Whether to run in dynamic mode
            bytesio_pusher: Optional pusher for figure data
            logo: Optional logo for watermark
        """
        self._logger = get_logger(__name__)
        self._figure_size = figure_size
        self._show_figure = show_figure
        self._dynamic = dynamic
        self._bytesio_pusher = bytesio_pusher
        self._logo = logo
        
        # Figure and axes
        self._fig: Optional[Figure] = None
        self._axes = {}
        
        # Timer for dynamic updates
        self._timer = None
        
        self._setup_backend()
    
    def _setup_backend(self) -> None:
        """Setup matplotlib backend."""
        self._logger.info(f"Default backend: {get_backend()}")
        
        if not self._show_figure and self._dynamic:
            plt.close('all')
            backend = 'Agg'
            use(backend)
            self._logger.info(f"Switched to backend \"{backend}\" for background drawing")
        
        # Hide toolbar
        plt.rcParams['toolbar'] = 'None'
    
    @property
    def figure(self) -> Optional[Figure]:
        """Get the figure."""
        return self._fig
    
    @property
    def axes(self) -> dict:
        """Get all axes."""
        return self._axes.copy()
    
    def get_axis(self, name: str):
        """Get a specific axis.
        
        Args:
            name: Axis name
            
        Returns:
            Matplotlib axis or None if not found
        """
        return self._axes.get(name)
    
    def create_figure(self) -> Figure:
        """Create a new figure.
        
        Returns:
            Created figure
        """
        self._fig = plt.figure(figsize=self._figure_size)
        self._logger.debug(f"Figure created with size {self._figure_size}")
        return self._fig
    
    def add_axis(self, name: str, *args, **kwargs):
        """Add an axis to the figure.
        
        Args:
            name: Axis name
            *args: Arguments for add_subplot
            **kwargs: Keyword arguments for add_subplot
            
        Returns:
            Created axis
        """
        if self._fig is None:
            raise RuntimeError("Figure must be created before adding axes")
        
        ax = self._fig.add_subplot(*args, **kwargs)
        self._axes[name] = ax
        self._logger.debug(f"Axis '{name}' added")
        return ax
    
    def create_gridspec_layout(self, nrows: int, ncols: int, **kwargs) -> gridspec.GridSpec:
        """Create a gridspec layout.
        
        Args:
            nrows: Number of rows
            ncols: Number of columns
            **kwargs: Additional gridspec arguments
            
        Returns:
            GridSpec object
        """
        if self._fig is None:
            raise RuntimeError("Figure must be created before creating gridspec")
        
        gs = gridspec.GridSpec(nrows, ncols, figure=self._fig, **kwargs)
        self._logger.debug(f"GridSpec created with {nrows}x{ncols} layout")
        return gs
    
    def add_watermark(self, size: Tuple[float, float] = (0.1, 0.1), alpha: float = 0.02) -> None:
        """Add watermark to the figure.
        
        Args:
            size: Watermark size
            alpha: Watermark transparency
        """
        if self._fig is None or self._logo is None:
            return
        
        try:
            add_watermark2(self._fig, self._logo, size=size, alpha=alpha)
            self._logger.debug("Watermark added to figure")
        except Exception as e:
            self._logger.warning(f"Failed to add watermark: {e}")
    
    def save_figure(self, filename: str, dpi: int = 300, **kwargs) -> None:
        """Save figure to file.
        
        Args:
            filename: Output filename
            dpi: Resolution in DPI
            **kwargs: Additional savefig arguments
        """
        if self._fig is None:
            raise RuntimeError("No figure to save")
        
        self._fig.savefig(filename, dpi=dpi, **kwargs)
        self._logger.info(f"Figure saved to: {filename}")
    
    def push_figure_data(self) -> None:
        """Push figure data to BytesIO pusher."""
        if self._fig is None or self._bytesio_pusher is None:
            return
        
        try:
            fig_buffer = BytesIO()
            self._fig.savefig(fig_buffer, format='png')
            fig_buffer.seek(0)
            self._bytesio_pusher.push(fig_buffer)
            fig_buffer.close()
            self._logger.debug("Figure data pushed")
        except Exception as e:
            self._logger.error(f"Failed to push figure data: {e}")
    
    def redraw(self) -> None:
        """Redraw the figure."""
        if self._fig is None:
            return
        
        try:
            self._fig.canvas.draw_idle()
            self._fig.canvas.flush_events()
        except Exception as e:
            self._logger.warning(f"Failed to redraw figure: {e}")
    
    def tight_layout(self) -> None:
        """Apply tight layout to the figure."""
        if self._fig is None:
            return
        
        try:
            plt.tight_layout()
            self._logger.debug("Tight layout applied")
        except Exception as e:
            self._logger.warning(f"Failed to apply tight layout: {e}")
    
    def show(self) -> None:
        """Show the figure."""
        if self._fig is None or not self._show_figure:
            return
        
        plt.show()
    
    def start_dynamic_updates(self, interval: float, callback) -> None:
        """Start dynamic updates with a timer.
        
        Args:
            interval: Update interval in milliseconds
            callback: Callback function for updates
        """
        if self._fig is None or not self._dynamic:
            return
        
        self._timer = self._fig.canvas.new_timer(interval=int(interval * 1000))
        self._timer.add_callback(callback)
        self._timer.start()
        self._logger.debug(f"Dynamic updates started with interval {interval}s")
    
    def stop_dynamic_updates(self) -> None:
        """Stop dynamic updates."""
        if self._timer is not None:
            self._timer.stop()
            self._timer = None
            self._logger.debug("Dynamic updates stopped")
    
    def close(self) -> None:
        """Close the figure."""
        if self._timer is not None:
            self.stop_dynamic_updates()
        
        if self._fig is not None:
            plt.close(self._fig)
            self._fig = None
            self._axes.clear()
            self._logger.debug("Figure closed")
    
    def cleanup(self) -> None:
        """Cleanup figure manager resources."""
        self.close()
        self._logger.debug("Figure manager cleanup completed")
