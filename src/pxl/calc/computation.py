#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from abc import ABC, abstractmethod
from typing import Tuple, List, Optional
import numpy as np

from ..metrics.de2k import DE2000
from ..util.logging_config import get_logger
from ..util.exceptions import CalculationError


class BaseComputation(ABC):
    """Base class for computation engines."""
    
    def __init__(self):
        self._logger = get_logger(self.__class__.__name__)
    
    @abstractmethod
    def compute(self, *args, **kwargs) -> float:
        """Perform computation."""
        pass
    
    @abstractmethod
    def validate_inputs(self, *args, **kwargs) -> None:
        """Validate computation inputs."""
        pass


class DeltaEComputation(BaseComputation):
    """Delta E computation engine."""
    
    def __init__(self, reference_white: Optional[np.ndarray] = None):
        """Initialize Delta E computation.
        
        Args:
            reference_white: Reference white XYZ values
        """
        super().__init__()
        self._reference_white = reference_white
        self._computation_count = 0
        self._total_delta_e = 0.0
        self._max_delta_e = 0.0
    
    @property
    def reference_white(self) -> Optional[np.ndarray]:
        """Get reference white."""
        return self._reference_white
    
    @property
    def computation_count(self) -> int:
        """Get number of computations performed."""
        return self._computation_count
    
    @property
    def average_delta_e(self) -> float:
        """Get average Delta E."""
        if self._computation_count == 0:
            return 0.0
        return self._total_delta_e / self._computation_count
    
    @property
    def max_delta_e(self) -> float:
        """Get maximum Delta E."""
        return self._max_delta_e
    
    def set_reference_white(self, reference_white: np.ndarray) -> None:
        """Set reference white.
        
        Args:
            reference_white: Reference white XYZ values
        """
        self.validate_xyz_input(reference_white)
        self._reference_white = reference_white.copy()
        self._logger.info(f"Reference white set to: {self._reference_white}")
    
    def validate_inputs(self, target_xyz: np.ndarray, measured_xyz: np.ndarray) -> None:
        """Validate Delta E computation inputs.
        
        Args:
            target_xyz: Target XYZ values
            measured_xyz: Measured XYZ values
            
        Raises:
            CalculationError: If inputs are invalid
        """
        self.validate_xyz_input(target_xyz)
        self.validate_xyz_input(measured_xyz)
        
        if self._reference_white is None:
            raise CalculationError("Reference white must be set before computation")
    
    def validate_xyz_input(self, xyz: np.ndarray) -> None:
        """Validate XYZ input.
        
        Args:
            xyz: XYZ values to validate
            
        Raises:
            CalculationError: If XYZ input is invalid
        """
        if not isinstance(xyz, np.ndarray):
            raise CalculationError("XYZ input must be a numpy array")
        
        if xyz.shape != (3,):
            raise CalculationError(f"XYZ input must have shape (3,), got {xyz.shape}")
        
        if np.any(xyz < 0):
            raise CalculationError("XYZ values must be non-negative")
        
        if np.any(np.isnan(xyz)) or np.any(np.isinf(xyz)):
            raise CalculationError("XYZ input contains invalid values (NaN or Inf)")
    
    def compute(self, target_xyz: np.ndarray, measured_xyz: np.ndarray, 
                scale_multiplier: float = 1.0) -> float:
        """Compute Delta E value.
        
        Args:
            target_xyz: Target XYZ values
            measured_xyz: Measured XYZ values
            scale_multiplier: Scale multiplier for target values
            
        Returns:
            Delta E value
            
        Raises:
            CalculationError: If computation fails
        """
        self.validate_inputs(target_xyz, measured_xyz)
        
        try:
            # Apply scale factor to target XYZ
            scaled_target = target_xyz * scale_multiplier
            
            # Compute Delta E
            delta_e, _, _, _ = DE2000(
                XYZ1=scaled_target,
                XYZ2=measured_xyz,
                XYZWP=self._reference_white
            )
            
            # Update statistics
            self._computation_count += 1
            self._total_delta_e += delta_e
            self._max_delta_e = max(self._max_delta_e, delta_e)
            
            self._logger.debug(f"Computed Delta E: {delta_e:.4f}")
            return float(delta_e)
            
        except Exception as e:
            self._logger.error(f"Delta E computation failed: {e}")
            raise CalculationError(f"Delta E computation failed: {e}")
    
    def reset_statistics(self) -> None:
        """Reset computation statistics."""
        self._computation_count = 0
        self._total_delta_e = 0.0
        self._max_delta_e = 0.0
        self._logger.debug("Delta E statistics reset")


class GDMBComputation(BaseComputation):
    """GDMB computation engine."""
    
    def __init__(self, tmax: float, scale_tolerance: float = 0.1, 
                 delta_tolerance: float = 0.1):
        """Initialize GDMB computation.
        
        Args:
            tmax: Maximum target value
            scale_tolerance: Scale tolerance
            delta_tolerance: Delta tolerance
        """
        super().__init__()
        self._tmax = tmax
        self._scale_tolerance = scale_tolerance
        self._delta_tolerance = delta_tolerance
        self._mmax = 0.0
        self._scale_ratio = 0.0
        self._measurement_data = []
        self._target_data = []
        self._scaled_data = []
    
    @property
    def tmax(self) -> float:
        """Get maximum target value."""
        return self._tmax
    
    @property
    def mmax(self) -> float:
        """Get maximum measured value."""
        return self._mmax
    
    @property
    def scale_ratio(self) -> float:
        """Get scale ratio."""
        return self._scale_ratio
    
    @property
    def measurement_data(self) -> List[float]:
        """Get measurement data."""
        return self._measurement_data.copy()
    
    @property
    def target_data(self) -> List[float]:
        """Get target data."""
        return self._target_data.copy()
    
    @property
    def scaled_data(self) -> List[float]:
        """Get scaled data."""
        return self._scaled_data.copy()
    
    def validate_inputs(self, target_y: float, measured_y: float) -> None:
        """Validate GDMB computation inputs.
        
        Args:
            target_y: Target Y value
            measured_y: Measured Y value
            
        Raises:
            CalculationError: If inputs are invalid
        """
        if not isinstance(target_y, (int, float)):
            raise CalculationError("Target Y must be a number")
        
        if not isinstance(measured_y, (int, float)):
            raise CalculationError("Measured Y must be a number")
        
        if target_y < 0 or measured_y < 0:
            raise CalculationError("Y values must be non-negative")
        
        if np.isnan(target_y) or np.isinf(target_y):
            raise CalculationError("Target Y contains invalid values")
        
        if np.isnan(measured_y) or np.isinf(measured_y):
            raise CalculationError("Measured Y contains invalid values")
    
    def compute(self, target_y: float, measured_y: float) -> bool:
        """Compute GDMB values and check for max flag.
        
        Args:
            target_y: Target Y value
            measured_y: Measured Y value
            
        Returns:
            True if max flag is reached, False otherwise
        """
        self.validate_inputs(target_y, measured_y)
        
        # Store data
        self._target_data.append(target_y)
        self._measurement_data.append(measured_y)
        
        # Update maximum measured value
        self._mmax = max(self._mmax, measured_y)
        
        # Check if we've reached the maximum
        max_flag = self._check_max_condition(target_y, measured_y)
        
        self._logger.debug(f"GDMB computation: target={target_y:.4f}, "
                          f"measured={measured_y:.4f}, max_flag={max_flag}")
        
        return max_flag
    
    def _check_max_condition(self, target_y: float, measured_y: float) -> bool:
        """Check if maximum condition is reached.
        
        Args:
            target_y: Target Y value
            measured_y: Measured Y value
            
        Returns:
            True if max condition is reached
        """
        # Simple implementation - can be enhanced based on specific requirements
        if len(self._measurement_data) < 2:
            return False
        
        # Check if we're close to the target maximum
        target_ratio = target_y / self._tmax if self._tmax > 0 else 0
        return target_ratio > (1.0 - self._delta_tolerance)
    
    def calculate_scale_factor(self) -> float:
        """Calculate scale factor.
        
        Returns:
            Scale factor value
        """
        if self._mmax > 0:
            self._scale_ratio = self._tmax / self._mmax
        else:
            self._scale_ratio = 1.0
        
        # Calculate scaled data
        self._scaled_data = [m * self._scale_ratio for m in self._measurement_data]
        
        self._logger.info(f"Scale factor calculated: {self._scale_ratio:.4f}")
        return self._scale_ratio
    
    def reset(self) -> None:
        """Reset computation state."""
        self._mmax = 0.0
        self._scale_ratio = 0.0
        self._measurement_data.clear()
        self._target_data.clear()
        self._scaled_data.clear()
        self._logger.debug("GDMB computation reset")
