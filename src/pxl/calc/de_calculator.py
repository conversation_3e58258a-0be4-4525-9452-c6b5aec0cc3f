#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from enum import Enum
from typing import Tuple, Optional
import numpy as np
import matplotlib.gridspec as gridspec

from .base import BaseCalculator, CalculatorConfig, CalculatorState, CalculatorFactory
from .data_manager import DataManager
from .computation import DeltaEComputation
from .state_manager import StateManager
from .figure_manager import FigureManager
from ..plot.sbc import SingleBarChart
from ..plot.dt import DynamicTable
from ..plot.slc import Scatter<PERSON><PERSON><PERSON>hart
from ..plot.cie1937 import CIE1931ChromaticityDiagram
from ..de_calc.report import DeltaECalculatorReport
from ..util.decorators import ensure_fixed_interval
from ..util.pusher import BytesIOPusher
from ..util.exceptions import ConfigurationError, CalculationError


class ScaleTarget(Enum):
    """Scale target enumeration."""
    AUTO = 'auto'
    NO = 'no'
    YES = 'yes'


class DeltaECalculatorConfig(CalculatorConfig):
    """Configuration for Delta E calculator."""
    
    def __init__(self, 
                 max_de_threshold: Tuple[float, float, float],
                 scale_target: ScaleTarget,
                 target_max: float = 0.0,
                 **kwargs):
        """Initialize Delta E calculator configuration.
        
        Args:
            max_de_threshold: Tuple of three threshold values for Delta E
            scale_target: Scaling target enumeration
            target_max: Maximum target value
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        
        # Validate inputs
        np_de_threshold = np.array(max_de_threshold, dtype=float)
        if not np_de_threshold.shape == (3,):
            raise ConfigurationError("Delta E threshold must have three float elements.")
        
        if not isinstance(scale_target, ScaleTarget):
            raise ConfigurationError(
                f"scale_target only accepts {[e.value for e in ScaleTarget]}"
            )
        
        self.max_de_threshold = max_de_threshold
        self.scale_target = scale_target
        self.target_max = target_max


class DeltaECalculatorRefactored(BaseCalculator):
    """
    Refactored Delta E Calculator with improved separation of concerns.
    
    This class demonstrates the single responsibility principle by delegating
    different concerns to specialized components:
    - DataManager: handles data validation and buffering
    - DeltaEComputation: performs Delta E calculations
    - StateManager: manages calculator state and timing
    - FigureManager: handles matplotlib figure operations
    """
    
    # Reference white for D65
    xyz_D65 = np.array([0.3127, 0.329, 0.3583])
    
    def __init__(self, config: DeltaECalculatorConfig):
        """Initialize the refactored Delta E Calculator.
        
        Args:
            config: Delta E calculator configuration
        """
        super().__init__(config)
        
        # Store specific config
        self._de_config = config
        
        # Initialize components
        self._data_manager = DataManager()
        self._computation = DeltaEComputation()
        self._state_manager = StateManager()
        self._figure_manager = FigureManager(
            figure_size=config.figure_size,
            show_figure=config.show_figure,
            dynamic=config.dynamic,
            bytesio_pusher=config.bytesio_pusher,
            logo=config.logo
        )
        
        # Plot components (will be initialized in initialize())
        self._diag_de = None
        self._diag_table = None
        self._diag_Y = None
        self._diag_cie1937 = None
        
        # Scale multiplier
        self._scale_multiplier = -1.0  # Invalid value that must be updated
        
        self._logger.info("DeltaE Calculator (Refactored) initialized")
    
    def initialize(self) -> None:
        """Initialize calculator components."""
        self._logger.info("Initializing DeltaE Calculator components...")
        
        # Create figure and layout
        self._figure_manager.create_figure()
        gs = self._figure_manager.create_gridspec_layout(3, 2)
        
        # Create axes
        ax_cie1937 = self._figure_manager.add_axis('cie1937', gs[:, 0])
        ax_Y = self._figure_manager.add_axis('Y', gs[0, 1])
        ax_DE = self._figure_manager.add_axis('DE', gs[1, 1])
        ax_table = self._figure_manager.add_axis('table', gs[2, 1])
        
        # Initialize plot components
        self._init_plot_components(ax_cie1937, ax_Y, ax_DE, ax_table)
        
        # Add watermark
        self._figure_manager.add_watermark()
        
        self.set_state(CalculatorState.INITIALIZED)
        self._logger.info("DeltaE Calculator initialization completed")
    
    def _init_plot_components(self, ax_cie1937, ax_Y, ax_DE, ax_table) -> None:
        """Initialize plot components."""
        np_de_threshold = np.array(self._de_config.max_de_threshold, dtype=float)
        
        # Initialize the CIE diagram
        self._diag_cie1937 = CIE1931ChromaticityDiagram(ax_cie1937)
        
        # Initialize the Y diagram
        self._diag_Y = ScatterLineChart(
            ax_Y,
            title="Y (Measurement vs. Target)",
            axis_style={'xlabel': 'Pattern', 'ylabel': 'Y'}
        )
        
        # Initialize the DE diagram
        self._diag_de = SingleBarChart(
            ax_DE,
            title="Delta E (ΔE) (Measurement vs. Target)",
            axis_style={'xlabel': 'Pattern', 'ylabel': 'Delta E (ΔE)'},
            thresholds=[
                (np_de_threshold[0], (0.5, 0.8, 0.5)),
                (np_de_threshold[1], (1.0, 0.7, 0.4)),
                (np_de_threshold[2], (0.9, 0.5, 0.5))
            ]
        )
        
        # Initialize the dynamic table
        self._diag_table = DynamicTable(
            ax_table,
            columns=['Index', 'X (target)', 'Y (target)', 'Z (target)',
                    'X (measure)', 'Y (measure)', 'Z (measure)', 'Delta E (ΔE)'],
            column_width=0.12,
        )
    
    def start(self) -> None:
        """Start the calculator."""
        if self._state != CalculatorState.INITIALIZED:
            raise RuntimeError("Calculator must be initialized before starting")
        
        self.set_state(CalculatorState.RUNNING)
        
        if self._dynamic:
            self._figure_manager.start_dynamic_updates(1.0, self._update_plot)
        
        self._logger.info("DeltaE Calculator started")
    
    def stop(self) -> None:
        """Stop the calculator."""
        self.set_state(CalculatorState.COMPLETED)
        self._state_manager.set_flag('stop_draw', True)
        self._figure_manager.stop_dynamic_updates()
        self._logger.info("DeltaE Calculator stopped")
    
    def reset(self) -> None:
        """Reset the calculator to initial state."""
        self.stop()
        self._data_manager.clear_history()
        self._computation.reset_statistics()
        self._state_manager.reset()
        self.set_state(CalculatorState.INITIALIZED)
        self._logger.info("DeltaE Calculator reset")
    
    def set_reference_white(self, target_XYZ: np.ndarray, measured_XYZ: np.ndarray) -> None:
        """Set the reference white for calculations.
        
        Args:
            target_XYZ: Target XYZ values
            measured_XYZ: Measured XYZ values
        """
        # Validate inputs
        self._data_manager.validate_xyz_data(target_XYZ)
        self._data_manager.validate_xyz_data(measured_XYZ)
        
        # Set reference white in both data manager and computation
        self._data_manager.set_reference_white(measured_XYZ)
        self._computation.set_reference_white(measured_XYZ)
        
        self._logger.info(f"Reference white set to: {measured_XYZ}")
    
    @ensure_fixed_interval(1.0)
    def receive_data(self, pattern_index: int, target_XYZ: np.ndarray, measured_XYZ: np.ndarray,
                     redraw: bool = True, interval: float = 1.0):
        """Receive and process measurement data.
        
        Args:
            pattern_index: Pattern index
            target_XYZ: Target XYZ values
            measured_XYZ: Measured XYZ values
            redraw: Whether to redraw plots
            interval: Update interval
        """
        try:
            data = (pattern_index, target_XYZ, measured_XYZ, redraw)
            self._data_manager.buffer.put(data, block=False)
        except Exception as e:
            self._logger.warning(f"Failed to receive data: {e}")
    
    def batch_receive_data(self, target_XYZ_list: np.ndarray, measured_XYZ_list: np.ndarray):
        """Receive batch data for processing.
        
        Args:
            target_XYZ_list: Array of target XYZ values
            measured_XYZ_list: Array of measured XYZ values
        """
        if len(target_XYZ_list) != len(measured_XYZ_list):
            raise CalculationError("Target and measured data lists must have the same length")
        
        for i, (target_xyz, measured_xyz) in enumerate(zip(target_XYZ_list, measured_XYZ_list)):
            self.receive_data(i, target_xyz, measured_xyz, redraw=False)

    def _update_plot(self, frame=None):
        """Update plots with new data."""
        if self._state_manager.get_flag('stop_draw'):
            return

        try:
            # Get data from buffer
            data = self._data_manager.buffer.get_nowait()
            pattern_index, target_XYZ, measured_XYZ, redraw = data

            # Set reference white if not set
            if self._data_manager.reference_white is None:
                self.set_reference_white(target_XYZ, measured_XYZ)

            # Update target max if needed
            if self._de_config.target_max <= 0.0:
                self._de_config.target_max = target_XYZ[1]
                if self._de_config.target_max <= 100.0:
                    self._de_config.target_max = 500.0

            # Update scale multiplier
            self._update_scale_multiplier(target_XYZ)

            # Perform computation
            delta_e = self._computation.compute(target_XYZ, measured_XYZ, self._scale_multiplier)

            # Update plots
            self._update_plot_components(pattern_index, target_XYZ, measured_XYZ, delta_e)

            # Store data point
            self._data_manager.add_data_point(pattern_index, {
                'target_xyz': target_XYZ,
                'measured_xyz': measured_XYZ,
                'delta_e': delta_e
            })

            # Redraw if needed
            if redraw:
                self._figure_manager.redraw()
                self._figure_manager.push_figure_data()

        except Exception as e:
            if "Empty" not in str(e):  # Don't log empty queue errors
                self._logger.debug(f"Update plot error: {e}")

    def _update_scale_multiplier(self, target_XYZ: np.ndarray):
        """Update scale multiplier based on scale target."""
        if self._de_config.scale_target == ScaleTarget.AUTO:
            self._scale_multiplier = self._de_config.target_max / target_XYZ[1] if target_XYZ[1] > 0 else 1.0
        elif self._de_config.scale_target == ScaleTarget.YES:
            self._scale_multiplier = self._de_config.target_max / target_XYZ[1] if target_XYZ[1] > 0 else 1.0
        else:  # NO
            self._scale_multiplier = 1.0

        self._data_manager.set_scale_multiplier(self._scale_multiplier)

    def _update_plot_components(self, pattern_index: int, target_XYZ: np.ndarray,
                               measured_XYZ: np.ndarray, delta_e: float):
        """Update individual plot components."""
        # Apply scale factor to target_XYZ for display
        xyz_t = target_XYZ * self._scale_multiplier
        xyz_m = measured_XYZ

        # Update Delta E chart
        self._diag_de.add_value(pattern_index, delta_e, redraw=False)

        # Update CIE diagram
        self._diag_cie1937.add_a_color(xyz_t, redraw=False)
        self._diag_cie1937.add_b_color(xyz_m, redraw=False)

        # Update Y scatter chart
        self._diag_Y.add_value_pair(pattern_index, target_XYZ[1], measured_XYZ[1], redraw=False)

        # Update table
        table_row = [
            pattern_index,
            f"{target_XYZ[0]:.4f}", f"{target_XYZ[1]:.4f}", f"{target_XYZ[2]:.4f}",
            f"{measured_XYZ[0]:.4f}", f"{measured_XYZ[1]:.4f}", f"{measured_XYZ[2]:.4f}",
            f"{delta_e:.4f}"
        ]
        self._diag_table.add_external_data(table_row, redraw=False)

    def plot_figure(self, save_figure: Optional[str] = None):
        """Plot the figure with all components.

        Args:
            save_figure: Optional filename to save the figure
        """
        if not self._dynamic:
            # Process all data in buffer for static mode
            while not self._data_manager.buffer.empty():
                self._update_plot()

        self._figure_manager.tight_layout()

        if save_figure:
            self._figure_manager.save_figure(save_figure)

        self._figure_manager.show()

    def done(self):
        """Signal completion of data processing."""
        self.stop()
        self._logger.info("DeltaE calculation completed")

    def get_report(self) -> DeltaECalculatorReport:
        """Get calculation report.

        Returns:
            Delta E calculation report
        """
        return DeltaECalculatorReport(
            max_de=self._computation.max_delta_e,
            avg_de=self._computation.average_delta_e
        )

    def cleanup(self) -> None:
        """Cleanup calculator resources."""
        super().cleanup()
        self._figure_manager.cleanup()
        self._state_manager.cleanup()


# Register the refactored calculator
CalculatorFactory.register('delta_e_refactored', DeltaECalculatorRefactored)
