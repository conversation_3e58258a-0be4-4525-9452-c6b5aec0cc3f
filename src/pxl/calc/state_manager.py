#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import threading
import time
from typing import Dict, Any, Optional, Callable
from enum import Enum

from ..util.logging_config import get_logger
from .base import CalculatorState


class TimerState(Enum):
    """Timer state enumeration."""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"


class StateManager:
    """Manages calculator state and timing operations."""
    
    def __init__(self):
        """Initialize state manager."""
        self._logger = get_logger(__name__)
        self._state = CalculatorState.INITIALIZED
        self._flags = {}
        self._counters = {}
        self._timers = {}
        self._lock = threading.RLock()
        
        # Common flags
        self.set_flag('stop_draw', False)
        self.set_flag('receive_flag', False)
        self.set_flag('max_flag', False)
        
        # Common counters
        self.set_counter('max_counter', 0)
        self.set_counter('pattern_index', 0)
    
    @property
    def state(self) -> CalculatorState:
        """Get current state."""
        with self._lock:
            return self._state
    
    def set_state(self, state: CalculatorState) -> None:
        """Set calculator state.
        
        Args:
            state: New state to set
        """
        with self._lock:
            old_state = self._state
            self._state = state
            self._logger.debug(f"State changed from {old_state.value} to {state.value}")
    
    def get_flag(self, name: str) -> bool:
        """Get flag value.
        
        Args:
            name: Flag name
            
        Returns:
            Flag value
        """
        with self._lock:
            return self._flags.get(name, False)
    
    def set_flag(self, name: str, value: bool) -> None:
        """Set flag value.
        
        Args:
            name: Flag name
            value: Flag value
        """
        with self._lock:
            self._flags[name] = value
            self._logger.debug(f"Flag '{name}' set to {value}")
    
    def toggle_flag(self, name: str) -> bool:
        """Toggle flag value.
        
        Args:
            name: Flag name
            
        Returns:
            New flag value
        """
        with self._lock:
            current = self._flags.get(name, False)
            new_value = not current
            self._flags[name] = new_value
            self._logger.debug(f"Flag '{name}' toggled from {current} to {new_value}")
            return new_value
    
    def get_counter(self, name: str) -> int:
        """Get counter value.
        
        Args:
            name: Counter name
            
        Returns:
            Counter value
        """
        with self._lock:
            return self._counters.get(name, 0)
    
    def set_counter(self, name: str, value: int) -> None:
        """Set counter value.
        
        Args:
            name: Counter name
            value: Counter value
        """
        with self._lock:
            self._counters[name] = value
            self._logger.debug(f"Counter '{name}' set to {value}")
    
    def increment_counter(self, name: str, step: int = 1) -> int:
        """Increment counter.
        
        Args:
            name: Counter name
            step: Increment step
            
        Returns:
            New counter value
        """
        with self._lock:
            current = self._counters.get(name, 0)
            new_value = current + step
            self._counters[name] = new_value
            self._logger.debug(f"Counter '{name}' incremented from {current} to {new_value}")
            return new_value
    
    def decrement_counter(self, name: str, step: int = 1) -> int:
        """Decrement counter.
        
        Args:
            name: Counter name
            step: Decrement step
            
        Returns:
            New counter value
        """
        with self._lock:
            current = self._counters.get(name, 0)
            new_value = max(0, current - step)  # Don't go below 0
            self._counters[name] = new_value
            self._logger.debug(f"Counter '{name}' decremented from {current} to {new_value}")
            return new_value
    
    def reset_counter(self, name: str) -> None:
        """Reset counter to zero.
        
        Args:
            name: Counter name
        """
        with self._lock:
            self._counters[name] = 0
            self._logger.debug(f"Counter '{name}' reset to 0")
    
    def create_timer(self, name: str, interval: float, callback: Callable) -> None:
        """Create a timer.
        
        Args:
            name: Timer name
            interval: Timer interval in seconds
            callback: Callback function
        """
        with self._lock:
            if name in self._timers:
                self.stop_timer(name)
            
            timer = threading.Timer(interval, callback)
            self._timers[name] = {
                'timer': timer,
                'interval': interval,
                'callback': callback,
                'state': TimerState.STOPPED
            }
            self._logger.debug(f"Timer '{name}' created with interval {interval}s")
    
    def start_timer(self, name: str) -> bool:
        """Start a timer.
        
        Args:
            name: Timer name
            
        Returns:
            True if timer was started, False if not found
        """
        with self._lock:
            if name not in self._timers:
                self._logger.warning(f"Timer '{name}' not found")
                return False
            
            timer_info = self._timers[name]
            if timer_info['state'] == TimerState.RUNNING:
                self._logger.warning(f"Timer '{name}' is already running")
                return False
            
            timer_info['timer'].start()
            timer_info['state'] = TimerState.RUNNING
            self._logger.debug(f"Timer '{name}' started")
            return True
    
    def stop_timer(self, name: str) -> bool:
        """Stop a timer.
        
        Args:
            name: Timer name
            
        Returns:
            True if timer was stopped, False if not found
        """
        with self._lock:
            if name not in self._timers:
                self._logger.warning(f"Timer '{name}' not found")
                return False
            
            timer_info = self._timers[name]
            timer_info['timer'].cancel()
            timer_info['state'] = TimerState.STOPPED
            self._logger.debug(f"Timer '{name}' stopped")
            return True
    
    def stop_all_timers(self) -> None:
        """Stop all timers."""
        with self._lock:
            for name in list(self._timers.keys()):
                self.stop_timer(name)
            self._logger.debug("All timers stopped")
    
    def get_timer_state(self, name: str) -> Optional[TimerState]:
        """Get timer state.
        
        Args:
            name: Timer name
            
        Returns:
            Timer state or None if not found
        """
        with self._lock:
            if name not in self._timers:
                return None
            return self._timers[name]['state']
    
    def get_state_summary(self) -> Dict[str, Any]:
        """Get state summary.
        
        Returns:
            Dictionary with state information
        """
        with self._lock:
            return {
                'calculator_state': self._state.value,
                'flags': self._flags.copy(),
                'counters': self._counters.copy(),
                'timers': {name: info['state'].value for name, info in self._timers.items()}
            }
    
    def reset(self) -> None:
        """Reset state manager."""
        with self._lock:
            # Stop all timers
            self.stop_all_timers()
            
            # Reset state
            self._state = CalculatorState.INITIALIZED
            
            # Reset flags
            for flag in self._flags:
                self._flags[flag] = False
            
            # Reset counters
            for counter in self._counters:
                self._counters[counter] = 0
            
            # Clear timers
            self._timers.clear()
            
            self._logger.debug("State manager reset")
    
    def cleanup(self) -> None:
        """Cleanup state manager resources."""
        self.stop_all_timers()
        self._logger.debug("State manager cleanup completed")
