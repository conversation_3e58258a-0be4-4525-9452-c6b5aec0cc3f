#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import queue
from typing import List, Tuple, Optional, Any
import numpy as np
from collections import deque

from ..util.logging_config import get_logger
from ..util.exceptions import DataProcessingError


class DataBuffer:
    """Thread-safe data buffer for calculator data."""
    
    def __init__(self, maxsize: int = 0):
        """Initialize data buffer.
        
        Args:
            maxsize: Maximum buffer size (0 for unlimited)
        """
        self._queue = queue.Queue(maxsize=maxsize)
        self._logger = get_logger(__name__)
    
    def put(self, data: Any, block: bool = True, timeout: Optional[float] = None) -> None:
        """Put data into buffer.
        
        Args:
            data: Data to put
            block: Whether to block if buffer is full
            timeout: Timeout for blocking operation
            
        Raises:
            queue.Full: If buffer is full and block=False
        """
        try:
            self._queue.put(data, block=block, timeout=timeout)
        except queue.Full:
            self._logger.warning("Data buffer is full, dropping data")
            raise
    
    def get(self, block: bool = True, timeout: Optional[float] = None) -> Any:
        """Get data from buffer.
        
        Args:
            block: Whether to block if buffer is empty
            timeout: Timeout for blocking operation
            
        Returns:
            Data from buffer
            
        Raises:
            queue.Empty: If buffer is empty and block=False
        """
        return self._queue.get(block=block, timeout=timeout)
    
    def get_nowait(self) -> Any:
        """Get data without blocking.
        
        Returns:
            Data from buffer
            
        Raises:
            queue.Empty: If buffer is empty
        """
        return self._queue.get_nowait()
    
    def empty(self) -> bool:
        """Check if buffer is empty."""
        return self._queue.empty()
    
    def qsize(self) -> int:
        """Get buffer size."""
        return self._queue.qsize()
    
    def clear(self) -> None:
        """Clear all data from buffer."""
        while not self._queue.empty():
            try:
                self._queue.get_nowait()
            except queue.Empty:
                break


class DataManager:
    """Manages data processing and validation for calculators."""
    
    def __init__(self, buffer_size: int = 100):
        """Initialize data manager.
        
        Args:
            buffer_size: Size of data buffer
        """
        self._buffer = DataBuffer(maxsize=buffer_size)
        self._logger = get_logger(__name__)
        self._data_history = deque(maxlen=1000)  # Keep last 1000 data points
        self._reference_white = None
        self._scale_multiplier = 1.0
    
    @property
    def buffer(self) -> DataBuffer:
        """Get data buffer."""
        return self._buffer
    
    @property
    def reference_white(self) -> Optional[np.ndarray]:
        """Get reference white."""
        return self._reference_white
    
    @property
    def scale_multiplier(self) -> float:
        """Get scale multiplier."""
        return self._scale_multiplier
    
    def set_reference_white(self, reference_white: np.ndarray) -> None:
        """Set reference white.
        
        Args:
            reference_white: Reference white XYZ values
        """
        if not isinstance(reference_white, np.ndarray):
            raise DataProcessingError("Reference white must be a numpy array")
        
        if reference_white.shape != (3,):
            raise DataProcessingError("Reference white must have shape (3,)")
        
        self._reference_white = reference_white.copy()
        self._logger.info(f"Reference white set to: {self._reference_white}")
    
    def set_scale_multiplier(self, multiplier: float) -> None:
        """Set scale multiplier.
        
        Args:
            multiplier: Scale multiplier value
        """
        if multiplier <= 0:
            raise DataProcessingError("Scale multiplier must be positive")
        
        self._scale_multiplier = multiplier
        self._logger.debug(f"Scale multiplier set to: {self._scale_multiplier}")
    
    def validate_xyz_data(self, xyz_data: np.ndarray) -> None:
        """Validate XYZ data.
        
        Args:
            xyz_data: XYZ data to validate
            
        Raises:
            DataProcessingError: If data is invalid
        """
        if not isinstance(xyz_data, np.ndarray):
            raise DataProcessingError("XYZ data must be a numpy array")
        
        if xyz_data.shape != (3,):
            raise DataProcessingError(f"XYZ data must have shape (3,), got {xyz_data.shape}")
        
        if np.any(xyz_data < 0):
            raise DataProcessingError("XYZ values must be non-negative")
        
        if np.any(np.isnan(xyz_data)) or np.any(np.isinf(xyz_data)):
            raise DataProcessingError("XYZ data contains invalid values (NaN or Inf)")
    
    def validate_y_data(self, y_value: float) -> None:
        """Validate Y data.
        
        Args:
            y_value: Y value to validate
            
        Raises:
            DataProcessingError: If data is invalid
        """
        if not isinstance(y_value, (int, float)):
            raise DataProcessingError("Y value must be a number")
        
        if y_value < 0:
            raise DataProcessingError("Y value must be non-negative")
        
        if np.isnan(y_value) or np.isinf(y_value):
            raise DataProcessingError("Y value contains invalid values (NaN or Inf)")
    
    def add_data_point(self, pattern_index: int, data: Any) -> None:
        """Add data point to history.
        
        Args:
            pattern_index: Pattern index
            data: Data to add
        """
        self._data_history.append((pattern_index, data))
    
    def get_data_history(self) -> List[Tuple[int, Any]]:
        """Get data history.
        
        Returns:
            List of (pattern_index, data) tuples
        """
        return list(self._data_history)
    
    def clear_history(self) -> None:
        """Clear data history."""
        self._data_history.clear()
        self._logger.debug("Data history cleared")
    
    def get_statistics(self) -> dict:
        """Get data statistics.
        
        Returns:
            Dictionary with statistics
        """
        return {
            'buffer_size': self._buffer.qsize(),
            'history_size': len(self._data_history),
            'has_reference_white': self._reference_white is not None,
            'scale_multiplier': self._scale_multiplier
        }
