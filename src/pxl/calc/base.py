#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Tuple, Optional, Any
import numpy as np

from ..util.logging_config import get_logger
from ..util.pusher import BytesIOPusher


class CalculatorState(Enum):
    """Calculator state enumeration."""
    INITIALIZED = "initialized"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class CalculatorConfig:
    """Base configuration for calculators."""
    dynamic: bool = True
    show_figure: bool = True
    figure_size: Tuple[float, float] = (12.0, 7.0)
    title_size: int = 8
    title_color: str = 'purple'
    bytesio_pusher: Optional[BytesIOPusher] = None
    logo: Optional[Any] = None


class BaseCalculator(ABC):
    """Base class for all calculators with common functionality."""
    
    def __init__(self, config: CalculatorConfig):
        """Initialize base calculator.
        
        Args:
            config: Calculator configuration
        """
        self._config = config
        self._state = CalculatorState.INITIALIZED
        self._logger = get_logger(self.__class__.__name__)
        
        # Common attributes
        self._dynamic = config.dynamic
        self._show_figure = config.show_figure
        self._bytesio_pusher = config.bytesio_pusher
        self._logo = config.logo
        
        # State tracking
        self._stop_draw = False
        self._timer = None
        
        self._logger.info(f"Initialized {self.__class__.__name__}")
    
    @property
    def state(self) -> CalculatorState:
        """Get current calculator state."""
        return self._state
    
    @property
    def config(self) -> CalculatorConfig:
        """Get calculator configuration."""
        return self._config
    
    @property
    def is_dynamic(self) -> bool:
        """Check if calculator is in dynamic mode."""
        return self._dynamic
    
    @property
    def is_running(self) -> bool:
        """Check if calculator is currently running."""
        return self._state == CalculatorState.RUNNING
    
    def set_state(self, state: CalculatorState) -> None:
        """Set calculator state.
        
        Args:
            state: New state to set
        """
        old_state = self._state
        self._state = state
        self._logger.debug(f"State changed from {old_state.value} to {state.value}")
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize calculator components."""
        pass
    
    @abstractmethod
    def start(self) -> None:
        """Start the calculator."""
        pass
    
    @abstractmethod
    def stop(self) -> None:
        """Stop the calculator."""
        pass
    
    @abstractmethod
    def reset(self) -> None:
        """Reset the calculator to initial state."""
        pass
    
    @abstractmethod
    def get_report(self) -> Any:
        """Get calculation report."""
        pass
    
    def pause(self) -> None:
        """Pause the calculator."""
        if self._state == CalculatorState.RUNNING:
            self.set_state(CalculatorState.PAUSED)
            self._logger.info("Calculator paused")
    
    def resume(self) -> None:
        """Resume the calculator."""
        if self._state == CalculatorState.PAUSED:
            self.set_state(CalculatorState.RUNNING)
            self._logger.info("Calculator resumed")
    
    def cleanup(self) -> None:
        """Cleanup calculator resources."""
        if self._timer:
            self._timer.stop()
            self._timer = None
        
        self._stop_draw = True
        self._logger.info("Calculator cleanup completed")
    
    def __enter__(self):
        """Context manager entry."""
        self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()


class CalculatorFactory:
    """Factory for creating calculator instances."""
    
    _calculators = {}
    
    @classmethod
    def register(cls, name: str, calculator_class: type):
        """Register a calculator class.
        
        Args:
            name: Calculator name
            calculator_class: Calculator class
        """
        cls._calculators[name] = calculator_class
    
    @classmethod
    def create(cls, name: str, *args, **kwargs) -> BaseCalculator:
        """Create a calculator instance.
        
        Args:
            name: Calculator name
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Calculator instance
            
        Raises:
            ValueError: If calculator name is not registered
        """
        if name not in cls._calculators:
            raise ValueError(f"Unknown calculator: {name}")
        
        return cls._calculators[name](*args, **kwargs)
    
    @classmethod
    def list_calculators(cls) -> list:
        """List available calculator names."""
        return list(cls._calculators.keys())
