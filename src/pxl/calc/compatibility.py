#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from enum import Enum
from typing import Tuple, Optional
import numpy as np

from .de_calculator import DeltaECalculatorRefactored, DeltaECalculatorConfig, ScaleTarget
from ..util.logging_config import get_logger
from ..util.pusher import BytesIOPusher


class DeltaECalculatorAdapter:
    """
    Adapter class that provides backward compatibility with the original DeltaECalculator API
    while using the refactored implementation internally.
    """
    
    class EnumScaleTarget(Enum):
        """Original scale target enumeration for backward compatibility."""
        AUTO = 'auto'
        NO = 'no'
        YES = 'yes'
    
    xyz_D65 = np.array([0.3127, 0.329, 0.3583])
    
    def __init__(self, 
                 max_de_threshold: Tuple[float, float, float],
                 scale_target: EnumScaleTarget,
                 dynamic: bool = True,
                 target_max: float = 0.0,
                 figure_size: Tuple[float, float] = (12.0, 7.0),
                 title_size: int = 8,
                 title_color: str = 'purple',
                 show_figure: bool = True,
                 bytesio_pusher: Optional[BytesIOPusher] = None,
                 logo=None):
        """Initialize the adapter with original API parameters.
        
        Args:
            max_de_threshold: Tuple of three threshold values for Delta E
            scale_target: Scaling target enumeration
            dynamic: Whether to run in dynamic mode
            target_max: Maximum target value
            figure_size: Size of the figure
            title_size: Size of the title
            title_color: Color of the title
            show_figure: Whether to show the figure
            bytesio_pusher: Optional pusher for figure data
            logo: Optional logo for watermark
        """
        self._logger = get_logger(__name__)
        
        # Convert original enum to new enum
        # Handle both the adapter's enum and the original DeltaECalculator enum
        if hasattr(scale_target, 'value'):
            # It's an enum, get the value
            scale_value = scale_target.value
        else:
            # It's already a string value
            scale_value = scale_target

        # Map string values to new enum
        scale_target_map = {
            'auto': ScaleTarget.AUTO,
            'no': ScaleTarget.NO,
            'yes': ScaleTarget.YES
        }

        if scale_value not in scale_target_map:
            raise ValueError(f"Invalid scale_target: {scale_value}")

        new_scale_target = scale_target_map[scale_value]
        
        # Create configuration for refactored calculator
        config = DeltaECalculatorConfig(
            max_de_threshold=max_de_threshold,
            scale_target=new_scale_target,
            target_max=target_max,
            dynamic=dynamic,
            show_figure=show_figure,
            figure_size=figure_size,
            title_size=title_size,
            title_color=title_color,
            bytesio_pusher=bytesio_pusher,
            logo=logo
        )
        
        # Create the refactored calculator
        self._calculator = DeltaECalculatorRefactored(config)
        
        # Initialize the calculator
        self._calculator.initialize()
        
        self._logger.info("DeltaE Calculator Adapter initialized")
    
    @property
    def state(self):
        """Get calculator state."""
        return self._calculator.state
    
    @property
    def is_dynamic(self) -> bool:
        """Check if calculator is in dynamic mode."""
        return self._calculator.is_dynamic
    
    def set_reference_white(self, target_XYZ: np.ndarray, measured_XYZ: np.ndarray) -> None:
        """Set the reference white for calculations.
        
        Args:
            target_XYZ: Target XYZ values
            measured_XYZ: Measured XYZ values
        """
        self._calculator.set_reference_white(target_XYZ, measured_XYZ)
    
    def receive_data(self, pattern_index: int, target_XYZ: np.ndarray, measured_XYZ: np.ndarray,
                     redraw: bool = True, interval: float = 1.0):
        """Receive and process measurement data.
        
        Args:
            pattern_index: Pattern index
            target_XYZ: Target XYZ values
            measured_XYZ: Measured XYZ values
            redraw: Whether to redraw plots
            interval: Update interval
        """
        self._calculator.receive_data(pattern_index, target_XYZ, measured_XYZ, redraw, interval)
    
    def batch_receive_data(self, target_XYZ_list: np.ndarray, measured_XYZ_list: np.ndarray):
        """Receive batch data for processing.
        
        Args:
            target_XYZ_list: Array of target XYZ values
            measured_XYZ_list: Array of measured XYZ values
        """
        self._calculator.batch_receive_data(target_XYZ_list, measured_XYZ_list)
    
    def plot_figure(self, save_figure: Optional[str] = None):
        """Plot the figure with all components.
        
        Args:
            save_figure: Optional filename to save the figure
        """
        # Start the calculator if not already started
        if self._calculator.state.value == 'initialized':
            self._calculator.start()
        
        self._calculator.plot_figure(save_figure)
    
    def done(self):
        """Signal completion of data processing."""
        self._calculator.done()
    
    def get_report(self):
        """Get calculation report."""
        return self._calculator.get_report()
    
    def reset(self):
        """Reset the calculator to initial state."""
        self._calculator.reset()
    
    def cleanup(self):
        """Cleanup calculator resources."""
        self._calculator.cleanup()
    
    # Context manager support
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()


def create_compatible_delta_e_calculator(*args, **kwargs) -> DeltaECalculatorAdapter:
    """Factory function to create a backward-compatible Delta E calculator.
    
    This function provides the same interface as the original DeltaECalculator
    constructor but returns an adapter that uses the refactored implementation.
    
    Args:
        *args: Positional arguments for DeltaECalculator
        **kwargs: Keyword arguments for DeltaECalculator
        
    Returns:
        DeltaECalculatorAdapter instance
    """
    return DeltaECalculatorAdapter(*args, **kwargs)
