#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

from enum import Enum
from typing import Tuple, Optional
import queue
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib import get_backend, use
from io import BytesIO

from ..core.interfaces import CalculatorEngine
from ..core.processors import DeltaEDataProcessor
from ..core.plot_manager import PlotManager
from ..plot.adapters import (
    SingleBar<PERSON>hart<PERSON>enderer, DynamicTableRenderer,
    ScatterLineChartRenderer, CIEDiagramRenderer
)
from ..plot.sbc import SingleBarChart
from ..plot.dt import DynamicTable
from ..plot.slc import ScatterLineChart
from ..plot.cie1937 import CIE1931ChromaticityDiagram
from ..util.decorators import ensure_fixed_interval
from ..util.watermark import add_watermark2
from ..util.pusher import BytesIOPusher
from ..util.logging_config import get_logger
from ..util.exceptions import ConfigurationError, CalculationError
from .report import DeltaECalculatorReport
from .de_calc import DeltaECalculator as OriginalDeltaECalculator


class DeltaECalculatorV2(CalculatorEngine):
    """
    Refactored Delta E Calculator with improved separation of concerns.
    Uses dependency injection and observer pattern for better modularity.
    """

    # Use the same enum as the original for compatibility
    EnumScaleTarget = OriginalDeltaECalculator.EnumScaleTarget
    xyz_D65 = np.array([0.3127, 0.329, 0.3583])

    def __init__(self, max_de_threshold: Tuple[float, float, float],
                 scale_target: EnumScaleTarget,
                 dynamic: bool = True, target_max: float = 0.0,
                 figure_size: Tuple[float, float] = (12.0, 7.0),
                 title_size: int = 8, title_color='purple',
                 show_figure: bool = True, 
                 bytesio_pusher: Optional[BytesIOPusher] = None,
                 logo=None,
                 plot_manager: Optional[PlotManager] = None):
        """Initialize the Delta E Calculator V2 with improved architecture.

        Args:
            max_de_threshold: Tuple of three threshold values for Delta E
            scale_target: Scaling target enumeration
            dynamic: Whether to run in dynamic mode
            target_max: Maximum target value
            figure_size: Size of the figure
            title_size: Size of the title
            title_color: Color of the title
            show_figure: Whether to show the figure
            bytesio_pusher: Optional pusher for figure data
            logo: Optional logo for watermark
            plot_manager: Optional custom plot manager
        """
        self._logger = get_logger(__name__)
        self._dynamic = dynamic
        self._show_figure = show_figure
        self._stop_draw = False
        self._bytesio_fig_pusher = bytesio_pusher
        self._timer = None
        self._tmax = target_max

        # Validate inputs
        np_de_threshold = np.array(max_de_threshold, dtype=float)
        if not np_de_threshold.shape == (3,):
            raise ConfigurationError("Delta E threshold must have three float elements.")
        
        if not isinstance(scale_target, self.EnumScaleTarget):
            raise ConfigurationError(
                f"scale_target only accepts {[e.value for e in self.EnumScaleTarget]}"
            )
        
        self._scale_target = scale_target
        self._scale_multiplier = -1.0  # an invalid value that must be updated
        self._data_queue = queue.Queue()
        self._xyz_ref_w = None
        self._de_store = []

        # Initialize data processor
        self._data_processor = DeltaEDataProcessor()

        # Setup matplotlib backend
        self._logger.info(f"Default backend: {get_backend()}")
        if (not self._show_figure) and self._dynamic:
            plt.close('all')
            backend = 'Agg'
            use(backend)
            self._logger.info(f"Switched to backend \"{backend}\" for background drawing")

        # Hide toolbar
        plt.rcParams['toolbar'] = 'None'

        # Create figure and subplots
        self._fig = plt.figure(figsize=figure_size)
        gs = gridspec.GridSpec(3, 2, figure=self._fig)
        
        self._ax_cie1937 = self._fig.add_subplot(gs[:, 0])
        self._ax_Y = self._fig.add_subplot(gs[0, 1])
        self._ax_DE = self._fig.add_subplot(gs[1, 1])
        self._ax_table = self._fig.add_subplot(gs[2, 1])

        # Initialize plot components
        self._init_plot_components(np_de_threshold)

        # Initialize plot manager
        if plot_manager is None:
            self._plot_manager = PlotManager(self._fig)
        else:
            self._plot_manager = plot_manager

        # Register renderers with plot manager
        self._register_renderers()

        # Add watermark if provided
        if logo:
            add_watermark2(self._fig, logo, size=(0.1, 0.1), alpha=0.02)

        # Initialize report
        self._report = DeltaECalculatorReport(max_de=0.0, avg_de=0.0)

    def _init_plot_components(self, np_de_threshold: np.ndarray) -> None:
        """Initialize plot components."""
        # Initialize the CIE diagram
        self._diag_cie1937 = CIE1931ChromaticityDiagram(self._ax_cie1937)

        # Initialize the Y diagram
        self._diag_Y = ScatterLineChart(
            self._ax_Y,
            title="Y (Measurement vs. Target)",
            axis_style={'xlabel': 'Pattern', 'ylabel': 'Y'}
        )

        # Initialize the DE diagram
        self._diag_de = SingleBarChart(
            self._ax_DE,
            title="Delta E (ΔE) (Measurement vs. Target)",
            axis_style={'xlabel': 'Pattern', 'ylabel': 'Delta E (ΔE)'},
            thresholds=[
                (np_de_threshold[0], (0.5, 0.8, 0.5)),
                (np_de_threshold[1], (1.0, 0.7, 0.4)),
                (np_de_threshold[2], (0.9, 0.5, 0.5))
            ]
        )

        # Initialize the dynamic table
        self._diag_table = DynamicTable(
            self._ax_table,
            columns=['Index', 'X (target)', 'Y (target)', 'Z (target)',
                    'X (measure)', 'Y (measure)', 'Z (measure)', 'Delta E (ΔE)'],
            column_width=0.12,
        )

    def _register_renderers(self) -> None:
        """Register plot renderers with the plot manager."""
        self._plot_manager.add_renderer('delta_e', SingleBarChartRenderer(self._diag_de))
        self._plot_manager.add_renderer('table', DynamicTableRenderer(self._diag_table))
        self._plot_manager.add_renderer('scatter_line', ScatterLineChartRenderer(self._diag_Y))
        self._plot_manager.add_renderer('chromaticity', CIEDiagramRenderer(self._diag_cie1937))

    def set_reference_white(self, target_XYZ: np.ndarray, measured_XYZ: np.ndarray) -> None:
        """Set the reference white for calculations."""
        if not isinstance(target_XYZ, np.ndarray) or not isinstance(measured_XYZ, np.ndarray):
            raise TypeError(f"Arguments must be numpy.ndarray. target_XYZ={target_XYZ}, measured_XYZ={measured_XYZ}")

        self._xyz_ref_w = measured_XYZ
        self._data_processor.set_reference_white(self._xyz_ref_w)
        self._logger.info(f"Reference white set to: {self._xyz_ref_w}")

    def calculate(self, pattern_index: int, target_xyz: np.ndarray, measured_xyz: np.ndarray) -> float:
        """Perform Delta E calculation."""
        plot_data_list = self._data_processor.process_data(pattern_index, target_xyz, measured_xyz)
        
        # Extract Delta E value from the first plot data (delta_e type)
        delta_e_value = 0.0
        for plot_data in plot_data_list:
            if plot_data.data_type == 'delta_e':
                delta_e_value = plot_data.data.get('value', 0.0)
                break
        
        return delta_e_value

    def get_report(self) -> DeltaECalculatorReport:
        """Get calculation report."""
        return self._report

    @ensure_fixed_interval(1.0)
    def receive_data(self, pattern_index: int, target_XYZ: np.ndarray, measured_XYZ: np.ndarray,
                     redraw: bool = True, interval: float = 1.0):
        """Receive and process measurement data."""
        try:
            self._data_queue.put((pattern_index, target_XYZ, measured_XYZ, redraw), block=False)
        except queue.Full:
            self._logger.warning("Data queue is full, dropping data point")

    def batch_receive_data(self, target_XYZ_list: np.ndarray, measured_XYZ_list: np.ndarray):
        """Receive batch data for processing."""
        if len(target_XYZ_list) != len(measured_XYZ_list):
            raise CalculationError("Target and measured data lists must have the same length")

        for i, (target_xyz, measured_xyz) in enumerate(zip(target_XYZ_list, measured_XYZ_list)):
            self.receive_data(i, target_xyz, measured_xyz, redraw=False)

    def plot_figure(self, save_figure: Optional[str] = None):
        """Plot the figure with all components."""
        if self._dynamic:
            self._timer = self._fig.canvas.new_timer(interval=1000)

            def timer_callback():
                self._update_plot(None)
                if not self._show_figure and self._bytesio_fig_pusher:
                    fig_buffer = BytesIO()
                    self._fig.savefig(fig_buffer, format='png')
                    fig_buffer.seek(0)
                    self._bytesio_fig_pusher.push(fig_buffer)
                    fig_buffer.close()
                if self._stop_draw:
                    self._timer.stop()

            self._timer.add_callback(timer_callback)
            self._timer.start()
        else:
            while not self._data_queue.empty():
                self._update_plot(None)

        plt.tight_layout()
        
        if save_figure:
            self._fig.savefig(save_figure)
            self._logger.info(f"Figure saved to: {save_figure}")

        if self._show_figure:
            plt.show()
        elif self._dynamic:
            while not self._stop_draw:
                plt.pause(0.1)

    def _update_plot(self, frame):
        """Update plots with new data."""
        data = self._update_data()
        if data:
            pattern_index, target_XYZ, measured_XYZ, redraw = data

            # Set reference white if not set
            if self._xyz_ref_w is None:
                self.set_reference_white(target_XYZ, measured_XYZ)

            # Set target max if not set
            if self._tmax <= 0.0:
                self._tmax = target_XYZ[1]
                if self._tmax <= 100.0:
                    self._tmax = 500.0

            # Update scale multiplier based on scale target
            self._update_scale_multiplier(target_XYZ)

            # Process data and update plots
            plot_data_list = self._data_processor.process_data(
                pattern_index, target_XYZ, measured_XYZ, redraw=redraw
            )

            # Update all plots through plot manager
            for plot_data in plot_data_list:
                self._plot_manager.update(plot_data)

            # Redraw if needed
            if redraw:
                self._plot_manager.redraw_all()

    def _update_data(self):
        """Get data from queue."""
        try:
            return self._data_queue.get_nowait()
        except queue.Empty:
            return None

    def _update_scale_multiplier(self, target_XYZ: np.ndarray):
        """Update scale multiplier based on scale target."""
        if self._scale_target == self.EnumScaleTarget.AUTO:
            self._scale_multiplier = self._tmax / target_XYZ[1] if target_XYZ[1] > 0 else 1.0
        elif self._scale_target == self.EnumScaleTarget.YES:
            self._scale_multiplier = self._tmax / target_XYZ[1] if target_XYZ[1] > 0 else 1.0
        else:  # NO
            self._scale_multiplier = 1.0

        self._data_processor.set_scale_multiplier(self._scale_multiplier)

    def done(self):
        """Signal completion of data processing."""
        self._stop_draw = True
        if self._timer:
            self._timer.stop()
        self._logger.info("Delta E calculation completed")
