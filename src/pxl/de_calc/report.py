#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2025 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Mar 30, 2025
"""

from dataclasses import dataclass


@dataclass
class DeltaECalculatorReport:
    """
    A data structure representing the result of a Delta E (ΔE) color difference calculation.

    Attributes:
        max_de (float): The maximum Delta E value observed.
        avg_de (float): The average Delta E value calculated over the sample set.

    """

    max_de: float
    avg_de: float
