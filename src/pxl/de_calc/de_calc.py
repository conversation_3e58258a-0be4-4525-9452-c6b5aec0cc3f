#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: June 18, 2024
"""

import queue
import time
import inspect
from collections.abc import Iterable
from enum import Enum
from typing import Tuple, Optional
from io import BytesIO

import matplotlib.gridspec as gridspec
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import MaxNLocator
from matplotlib import get_backend, use

from pxl.clrspc.xyz import visualize_XYZ
from pxl.metrics.de2k import DE2000
from pxl.plot.cie1937 import CIE1931ChromaticityDiagram
from pxl.plot.dt import DynamicTable
from pxl.plot.sbc import SingleBarChart
from pxl.plot.slc import ScatterLineChart
from pxl.util.decorators import ensure_fixed_interval
from pxl.util.watermark import add_watermark2
from pxl.util.pusher import BytesIOPusher
from pxl.util.thread import running_in_main_thread
from pxl.util.logging_config import get_logger
from pxl.de_calc.report import DeltaECalculatorReport


class DeltaECalculator:
    """
    A class to calculate Delta E values dynamically or statically from provided XYZ color coordinates.

    Note: This is the legacy implementation. For new projects, consider using DeltaECalculatorV2
    which provides better separation of concerns and improved modularity.
    """

    class EnumScaleTarget(Enum):
        AUTO = 'auto'
        NO = 'no'
        YES = 'yes'

    xyz_D65 = np.array([0.3127, 0.329, 0.3583])

    @classmethod
    def create_v2(cls, *args, **kwargs):
        """Factory method to create the new decoupled version.

        Returns:
            DeltaECalculatorV2 instance with improved architecture
        """
        from .de_calc_v2 import DeltaECalculatorV2
        logger = get_logger(__name__)
        logger.info("Creating DeltaECalculatorV2 with improved architecture")
        return DeltaECalculatorV2(*args, **kwargs)

    def __init__(self, max_de_threshold: Tuple[float, float, float],
                 scale_target: EnumScaleTarget,
                 dynamic: bool = True, target_max: float = 0.0,
                 figure_size: Tuple[float, float] = (12.0, 7.0),
                 title_size: int = 8, title_color='purple',
                 show_figure: bool = True, bytesio_pusher: Optional[BytesIOPusher] = None,
                 logo=None):
        """Initialize the Delta E Calculator with configurations for plotting and processing.

        Args:
            max_de_threshold (Tuple[float, float, float]): Thresholds for coloring Delta E values.
            scale_target (EnumScaleTarget): Enum to determine if scaling should be applied.
            dynamic (bool): Whether to update plots dynamically. Defaults to True.
            target_max (float): Maximum target value for scale normalization.
            figure_size (Tuple[float, float]): Size of the matplotlib figure. Defaults to (12.0, 7.0).
            title_size (int): Font size of the title. Defaults to 8.
            title_color (str): Color of the title. Defaults to 'purple'.
            show_figure (bool): Show figure if the value is True. Defaults to True.
            bytesio_pusher (BytesIOPusher): Push generated figure per each rendering in BytesIO format.
                                            Defaults to None.
            logo (str): Base64 encoded string of a logo image. Defaults to None.

        Raises:
            ValueError: If target_max is not positive or de_threshold doesn't have exactly 3 elements.
            TypeError: If scale_target is not an instance of EnumScaleTarget.
        """
        self._dynamic = dynamic  # determine dynamic mode at initialization
        self._show_figure = show_figure
        self._stop_draw = False
        self._bytesio_fig_pusher = bytesio_pusher
        self._timer = None

        print(f"{self.__class__.__name__}: Default backend: {get_backend()}")
        if (not self._show_figure) and self._dynamic:
            plt.close('all')
            backend = 'Agg'
            use(backend)
            print(f"{self.__class__.__name__}: Switched to backend \"{backend}\" for background drawing")

        #
        self._tmax = target_max

        np_de_threshold = np.array(max_de_threshold, dtype=float)
        if not np_de_threshold.shape == (3,):
            raise ValueError("Delta E threshold must have three float elements.")
        # 4947
        if not isinstance(scale_target, self.EnumScaleTarget):
            raise ValueError(
                f"{self.self.__class__.__name__}: scale_target only accepts "
                f"{[e.value for e in self.EnumScaleTarget]}"
            )
        self._scale_target = scale_target
        self._scale_multiplier = -1.0  # an invalid value that must be updated

        self._data_queue = queue.Queue()  # type: ignore[var-annotated]

        self._xyz_ref_w = None
        self._de_store = []  # type: ignore[var-annotated]

        # Hide toolbar
        plt.rcParams['toolbar'] = 'None'

        # Set global title properties
        plt.rcParams['axes.titlesize'] = title_size
        plt.rcParams['axes.titlecolor'] = title_color

        # Create a figure with a single subplot for the chromaticity diagram
        self._fig = plt.figure(figsize=figure_size, dpi=100)

        # Set the title of the window (this line should come after plt.show() for some backends)
        self._fig.canvas.manager.set_window_title('Delta E (ΔE) Calculator (prototype)')

        # Set the main title of the figure
        self._fig.suptitle('Delta E (ΔE) Calculator', fontsize=16)

        # Define a 2x2 grid specification
        gs = gridspec.GridSpec(3, 2, figure=self._fig, width_ratios=[50, 45], height_ratios=[60, 20, 20])

        # Create subplots
        self._ax_table = self._fig.add_subplot(gs[:, 0])
        self._ax_cie1937 = self._fig.add_subplot(gs[0, 1])  # A subplot in the second column, first row
        self._ax_Y = self._fig.add_subplot(gs[1, 1])  # A subplot in the second column, second row
        self._ax_DE = self._fig.add_subplot(gs[2, 1])  # A subplot in the second column, third row

        # Set integer-only locators
        self._ax_Y.xaxis.set_major_locator(MaxNLocator(integer=True))
        self._ax_Y.yaxis.set_major_locator(MaxNLocator(integer=True))
        self._ax_DE.xaxis.set_major_locator(MaxNLocator(integer=True))

        # Initialize the chromaticity diagram with the subplot
        self._diag_cie1937 = CIE1931ChromaticityDiagram(self._ax_cie1937)
        self._diag_cie1937.set_chroma_color_sets([
            ("Rec.709", ":", "green", [(0.64, 0.33), (0.30, 0.60), (0.15, 0.06)]),
            ("P3", ":", "purple", [(0.68, 0.32), (0.265, 0.69), (0.15, 0.06)]),
            ("Rec.2020", ":", "orange", [(0.708, 0.292), (0.170, 0.797), (0.131, 0.046)]),
        ])

        # Initialize the Y diagram with the subplot
        self._diag_Y = ScatterLineChart(
            self._ax_Y,
            title="Y (Measurement vs. Target)",
            style_line_a={
                'linewidth': 0.7, 'color': (0.9, 0.5, 0.5),
                'label': 'Target'
            },
            style_line_b={
                'linewidth': 0.7, 'color': (0.5, 0.5, 0.9),
                'marker': '.', 'markersize': 3,
                'label': 'Measurement'
            },
            axis_style={'xlabel': 'Pattern', 'ylabel': 'Y (nit)'}
        )

        # Initialize the DE diagram with the subplot
        self._diag_de = SingleBarChart(
            self._ax_DE,
            title="Delta E (ΔE) (Measurement vs. Target)",
            axis_style={
                'xlabel': 'Pattern',
                'ylabel': 'Delta E (ΔE)'
            },
            thresholds=[
                (np_de_threshold[0], (0.5, 0.8, 0.5)),  # Less saturated green
                (np_de_threshold[1], (1.0, 0.7, 0.4)),  # Less saturated orange
                (np_de_threshold[2], (0.9, 0.5, 0.5))  # Less saturated red
            ]
        )

        # Initialize the dynamic table
        self._diag_table = DynamicTable(
            self._ax_table,
            columns=['Index',
                     'X (target)', 'Y (target)', 'Z (target)',
                     'X (measure)', 'Y (measure)', 'Z (measure)',
                     'Delta E (ΔE)'],
            column_width=0.12,
        )

        if logo:
            add_watermark2(self._fig, logo, size=(0.1, 0.1), alpha=0.02)

        self._report: DeltaECalculatorReport = DeltaECalculatorReport(
            max_de=0.0,
            avg_de=0.0
        )

    def set_reference_white(self, target_XYZ: np.ndarray, measured_XYZ: np.ndarray):
        """Explicitly set the XYZ of reference white.

        Args:
            target_XYZ (np.ndarray): Target XYZ of reference white.
            measured_XYZ (np.ndarray): Measured XYZ of reference white.

        Raises:
            TypeError: If arguments are not numpy.ndarray instances.

        Note:
            If not explicitly called, reference white will be calculated with the 1st set of data.
        """
        if not isinstance(target_XYZ, np.ndarray) or not isinstance(measured_XYZ, np.ndarray):
            raise TypeError(f"Arguments must be numpy.ndarray. target_XYZ={target_XYZ}, measured_XYZ={measured_XYZ}")

        self._xyz_ref_w = self._cal_xyz_ref_w(self.xyz_D65, measured_XYZ, target_XYZ)
        print(f"{self.__class__.__name__}: reference white xyz = {self._xyz_ref_w}")

        if self._scale_target == self.EnumScaleTarget.NO:  # always not scale
            self._scale_multiplier = 1.0
        elif self._scale_target == self.EnumScaleTarget.YES:  # always scale
            self._scale_multiplier = self._de2k_scale_target(
                measured_XYZ[1], target_XYZ[1], -float('inf'), float('inf'))
        else:  # let the threshold decide
            self._scale_multiplier = self._de2k_scale_target(
                measured_XYZ[1], target_XYZ[1])
        print(f"{self.__class__.__name__}: "
              f"scale target? = {self._scale_target.value}, "
              f"multiplier = {self._scale_multiplier}")

    @ensure_fixed_interval(1.0)
    def receive_data(self, pattern_index: int, target_XYZ: np.ndarray, measured_XYZ: np.ndarray,
                     redraw: bool = True, interval: float = 1.0):
        """
        Execute a simulated meter measurement and ensure the total execution time
        meets a specified interval.

        Args:
        - pattern_index (int): The index of the pattern for measurement.
        - target_XYZ (np.ndarray): target XYZ.
        - measured_XYZ (np.ndarray): measured XYZ.
        - redraw (bool, optional): whether to redraw the graph.
        - interval (float, optional): The total time that the function call should take. If not specified,
          a default of 1 second is used. The value cannot be lower than 1 second to ensure rendering time.
        """
        if not isinstance(target_XYZ, np.ndarray) or not isinstance(measured_XYZ, np.ndarray):
            raise TypeError(f"Arguments must be numpy.ndarray. target_XYZ={target_XYZ}, measured_XYZ={measured_XYZ}")

        self._data_queue.put((pattern_index, target_XYZ, measured_XYZ, redraw))

    def batch_receive_data(self, target_XYZ_list: Iterable[np.ndarray], measured_XYZ_list: Iterable[np.ndarray]):
        """Execute a simulated meter measurement with specified interval.

        Args:
            pattern_index (int): The index of the pattern for measurement.
            target_XYZ (np.ndarray): Target XYZ values.
            measured_XYZ (np.ndarray): Measured XYZ values.
            redraw (bool): Whether to redraw the graph. Defaults to True.
            interval (float): Total execution time in seconds. Defaults to 1.0.

        Raises:
            TypeError: If target_XYZ or measured_XYZ are not numpy.ndarray instances.
        """
        if not isinstance(target_XYZ_list, Iterable) or not isinstance(measured_XYZ_list, Iterable):
            raise TypeError(
                f"Arguments must be iterable. "
                f"target_XYZ_list={target_XYZ_list}, measured_XYZ={measured_XYZ_list}"
            )

        l_target_XYZ_list, l_measured_XYZ_list = len(list(target_XYZ_list)), len(list(measured_XYZ_list))
        if l_target_XYZ_list != l_measured_XYZ_list:
            raise ValueError("The lengths of target XYZ list and measured XYZ list must match.")

        for index, (target_XYZ, measured_XYZ) in enumerate(zip(target_XYZ_list, measured_XYZ_list)):
            if not isinstance(target_XYZ, np.ndarray) or not isinstance(measured_XYZ, np.ndarray):
                raise TypeError(
                    f"Arguments must be numpy.ndarray. "
                    f"target_XYZ={target_XYZ}, measured_XYZ={measured_XYZ}"
                )
            # Use index as pattern_index
            self._data_queue.put((index, target_XYZ, measured_XYZ, index == l_target_XYZ_list - 1))

    def plot_figure(self, save_figure=None):
        """Control the plotting behavior.

        Args:
            save_figure (str, optional): Path to save the figure. Defaults to None.

        Note:
            This function must be called from the main thread.
        """
        if self._show_figure and self._dynamic:
            running_in_main_thread(inspect.currentframe().f_code.co_name)

        if self._dynamic:
            self._timer = self._fig.canvas.new_timer(interval=1000)

            def timer_callback():
                self._update_plot(None)
                if not self._show_figure:
                    fig_buffer: BytesIO = BytesIO()
                    self._fig.savefig(fig_buffer, format='png')
                    fig_buffer.seek(0)
                    self._bytesio_fig_pusher.push(fig_buffer)
                    fig_buffer.close()
                if self._stop_draw:
                    self._timer.stop()

            self._timer.add_callback(timer_callback)
            self._timer.start()
        else:
            while not self._data_queue.empty():
                self._update_plot(None)

        plt.tight_layout()
        if self._show_figure:
            plt.show()
        elif self._dynamic:
            while not self._stop_draw:
                # manually trigger Timer callback (ugly, but the only option found working with Agg backend so far)
                for callback, args, kwargs in self._timer.callbacks:
                    callback(*args, **kwargs)
                time.sleep(0.5)

        if save_figure:
            self._fig.savefig(save_figure, dpi=300)

    def done(self):
        """Send a signal to exit the process.

        Args:
            None

        Returns:
            None
        """
        if not self._dynamic:
            return

        print(f"{self.__class__.__name__}: Closing up the Delta E calculator...")
        self._stop_draw = True
        time.sleep(3)

        plt.close(self._fig)

    def get_report(self) -> DeltaECalculatorReport:
        """Send a signal to exit the process.

        Args:
            None

        Returns:
            (DeltaECalculatorReport): The Delta E Calculator report.
        """
        return self._report

    def _update_data(self):
        try:
            pattern_index, target_XYZ, measured_XYZ, redraw = self._data_queue.get_nowait()

            # Warning: assume the reference white is located in index zero if not set explicitly via set_reference_white
            if self._xyz_ref_w is None:
                self.set_reference_white(measured_XYZ, target_XYZ)

            # Warning: assume Tmax is located in index zero if not set explicitly via class initializer
            if self._tmax <= 0.0:
                self._tmax = target_XYZ[1]
                if self._tmax <= 100.0:
                    self._tmax = 500.0

            return pattern_index, target_XYZ, measured_XYZ, redraw
        except queue.Empty:
            return None

    def _init_plot(self):
        return ()

    def _update_plot(self, frame):
        data = self._update_data()
        if data:
            pattern_index, target_XYZ, measured_XYZ, redraw = data

            # Apply scale factor to target_XYZ
            XYZ_t = target_XYZ * self._scale_multiplier
            XYZ_m = measured_XYZ

            # Plot the points on chromaticity diagram
            self._diag_cie1937.add_a_color(XYZ_t, redraw)
            self._diag_cie1937.add_b_color(XYZ_m, redraw)

            # Plot the points on Y diagram
            self._diag_Y.add_value_pair(pattern_index, target_XYZ[1], measured_XYZ[1], redraw)

            # Calculate and plot Delta E
            DE, _, __, ___ = DE2000(
                XYZ1=np.array(XYZ_t),
                XYZ2=np.array(XYZ_m),
                XYZWP=np.array(self._xyz_ref_w)
            )
            self._diag_de.add_value(pattern_index, round(DE, 2), redraw)
            self._de_store.append(round(DE, 2))
            # Display max (excluding the 1st (reference white)) and average Delta E at run-time
            if len(self._de_store) > 1:
                _de_max, _de_avg = max(self._de_store[1:]), np.mean(self._de_store)
                textstr = f"Max: {_de_max:.2f}\nAvg: {_de_avg:.2f}"
                props = dict(boxstyle='round', facecolor='wheat', alpha=1.0)
                self._diag_de._ax.text(
                    0.02, 0.92, textstr,
                    transform=self._diag_de._ax.transAxes, fontsize=6,
                    verticalalignment='top', bbox=props
                )
                # update report
                self._report.max_de, self._report.avg_de = _de_max, _de_avg

            # Record detailed data in a table
            row = (
                [pattern_index]
                + [round(num, 4) for num in XYZ_t.tolist()]
                + [round(num, 4) for num in XYZ_m.tolist()]
                + [round(DE, 2)]
            )
            colors = [visualize_XYZ(XYZ=target_XYZ, max_Y=self._tmax)] + ['white'] * (len(row) - 1)
            self._diag_table.add_external_data(
                new_data=row,
                bg_colors=colors,
                redraw=redraw,
            )
            # Display reference white and scale factor
            if len(self._de_store) == 1:
                textstr = f"Reference white: {target_XYZ}; Relative: {self._scale_multiplier:.4f}"
                props = dict(boxstyle='round', facecolor='wheat', alpha=1.0)
                self._diag_table._ax.text(
                    0.025, 1.0, textstr,
                    transform=self._diag_table._ax.transAxes, fontsize=6,
                    verticalalignment='top', bbox=props
                )

    def _de2k_scale_target(self, Y_m: float, Y_t: float, thl: float = 0.9, thh: float = 1.2):
        relative = Y_m / Y_t
        return relative if thl <= relative <= thh else 1.0

    def _cal_xyz_ref_w(self, xyz_D65: np.array, XYZ_m: np.array, XYZ_t: np.array) -> np.array:
        """
        Calculate the adjusted XYZ values from the D65 standard illuminant values based on
        the maximum Y value from two sets of xyY color coordinates.
        """
        # Calculate the maximum Y value from both target and reference xyY values
        max_Y = max(XYZ_m[1], XYZ_t[1])

        # Compute the XYZ values adjusted for the maximum Y value
        # This adjusts the XYZ values proportionally to the max Y,
        # relative to the Y value of the D65 illuminant.
        adjusted_XYZ = xyz_D65 * max_Y / xyz_D65[1]

        return adjusted_XYZ
