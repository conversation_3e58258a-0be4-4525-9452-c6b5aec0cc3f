#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import os
from typing import Optional
from flask import Flask, jsonify, request, g
import numpy as np

from .security import SecurityConfig, SecurityMiddleware
from .validation import RequestValidator, handle_validation_errors
from .auth import AuthManager, APIKeyAuth, handle_auth_errors, DEFAULT_API_KEYS
from ..de_calc.de_calc import DeltaECalculator
from ..gdmb_calc.gdmb_calc import GDMBCalculator
from ..util.logging_config import get_logger
from ..util.pusher import BytesIOPusher


def create_secure_app(config: Optional[SecurityConfig] = None, 
                     auth_manager: Optional[AuthManager] = None) -> Flask:
    """Create a secure Flask application with PXL API endpoints.
    
    Args:
        config: Security configuration
        auth_manager: Authentication manager
        
    Returns:
        Configured Flask application
    """
    app = Flask(__name__)
    
    # Use default config if not provided
    if config is None:
        config = SecurityConfig()
    
    # Setup security middleware
    security = SecurityMiddleware(app, config)
    
    # Setup authentication
    if auth_manager is None:
        # Use API key authentication with default keys for development
        api_key_auth = APIKeyAuth(DEFAULT_API_KEYS)
        auth_manager = AuthManager(api_key_auth)
    
    # Setup logging
    logger = get_logger(__name__)
    
    # Health check endpoint (no auth required)
    @app.route('/health', methods=['GET'])
    @handle_validation_errors
    def health_check():
        """Health check endpoint."""
        return jsonify({
            'status': 'healthy',
            'service': 'PXL API',
            'version': '1.0.0'
        })
    
    # API info endpoint (no auth required)
    @app.route('/api/info', methods=['GET'])
    @handle_validation_errors
    def api_info():
        """API information endpoint."""
        return jsonify({
            'name': 'PXL API',
            'version': '1.0.0',
            'description': 'Pixel analysis and evaluation API',
            'endpoints': {
                '/health': 'Health check',
                '/api/info': 'API information',
                '/api/de/calculate': 'Delta E calculation',
                '/api/gdmb/calculate': 'GDMB calculation'
            },
            'authentication': 'API Key required (X-API-Key header)'
        })
    
    # Delta E calculation endpoint
    @app.route('/api/de/calculate', methods=['POST'])
    @handle_validation_errors
    @handle_auth_errors
    @security.rate_limit(requests_per_window=10, window_seconds=60)
    @auth_manager.require_auth('de_calc', 'calculate')
    @RequestValidator.validate_json_request(
        required_fields=['target_xyz', 'measured_xyz', 'max_de_threshold'],
        optional_fields=['scale_target', 'target_max', 'dynamic'],
        field_validators={
            'target_xyz': RequestValidator.validate_xyz_array,
            'measured_xyz': RequestValidator.validate_xyz_array,
            'max_de_threshold': RequestValidator.validate_de_threshold,
            'scale_target': lambda x: RequestValidator.validate_enum_value(
                x, ['auto', 'no', 'yes']
            ),
            'target_max': lambda x: RequestValidator.validate_number(x, min_val=0),
            'dynamic': RequestValidator.validate_boolean
        }
    )
    def calculate_delta_e():
        """Calculate Delta E values."""
        data = request.validated_data
        
        try:
            # Extract parameters
            target_xyz = np.array(data['target_xyz'])
            measured_xyz = np.array(data['measured_xyz'])
            max_de_threshold = tuple(data['max_de_threshold'])
            
            # Optional parameters
            scale_target_str = data.get('scale_target', 'auto')
            scale_target = getattr(DeltaECalculator.EnumScaleTarget, scale_target_str.upper())
            target_max = data.get('target_max', 0.0)
            dynamic = data.get('dynamic', False)
            
            # Create calculator
            calculator = DeltaECalculator(
                max_de_threshold=max_de_threshold,
                scale_target=scale_target,
                target_max=target_max,
                dynamic=dynamic,
                show_figure=False
            )
            
            # Set reference white and calculate
            calculator.set_reference_white(target_xyz, measured_xyz)
            
            # For single calculation, we can use the V2 calculator's calculate method
            if hasattr(calculator, 'calculate'):
                delta_e = calculator.calculate(0, target_xyz, measured_xyz)
            else:
                # Fallback for original calculator
                calculator.receive_data(0, target_xyz, measured_xyz)
                delta_e = 0.0  # Would need to extract from internal state
            
            logger.info(f"Delta E calculation completed for user {g.user['user_id']}")
            
            return jsonify({
                'status': 'success',
                'delta_e': float(delta_e),
                'target_xyz': target_xyz.tolist(),
                'measured_xyz': measured_xyz.tolist(),
                'parameters': {
                    'scale_target': scale_target_str,
                    'target_max': target_max
                }
            })
            
        except Exception as e:
            logger.error(f"Error in Delta E calculation: {str(e)}", exc_info=True)
            return jsonify({'error': 'Calculation failed'}), 500
    
    # GDMB calculation endpoint
    @app.route('/api/gdmb/calculate', methods=['POST'])
    @handle_validation_errors
    @handle_auth_errors
    @security.rate_limit(requests_per_window=10, window_seconds=60)
    @auth_manager.require_auth('gdmb_calc', 'calculate')
    @RequestValidator.validate_json_request(
        required_fields=['target_y', 'measured_y', 'tmax'],
        optional_fields=['debug', 'dynamic', 'scale_tolerance', 'delta_tolerance'],
        field_validators={
            'target_y': lambda x: RequestValidator.validate_number(x, min_val=0),
            'measured_y': lambda x: RequestValidator.validate_number(x, min_val=0),
            'tmax': lambda x: RequestValidator.validate_number(x, min_val=0),
            'debug': RequestValidator.validate_boolean,
            'dynamic': RequestValidator.validate_boolean,
            'scale_tolerance': lambda x: RequestValidator.validate_number(x, min_val=0, max_val=1),
            'delta_tolerance': lambda x: RequestValidator.validate_number(x, min_val=0, max_val=1)
        }
    )
    def calculate_gdmb():
        """Calculate GDMB values."""
        data = request.validated_data
        
        try:
            # Extract parameters
            target_y = float(data['target_y'])
            measured_y = float(data['measured_y'])
            tmax = float(data['tmax'])
            
            # Optional parameters
            debug = data.get('debug', False)
            dynamic = data.get('dynamic', False)
            scale_tolerance = data.get('scale_tolerance', 0.1)
            delta_tolerance = data.get('delta_tolerance', 0.1)
            
            # Create calculator
            calculator = GDMBCalculator(
                tmax=tmax,
                debug=debug,
                dynamic=dynamic,
                scale_tolerance=scale_tolerance,
                delta_tolerance=delta_tolerance,
                show_figure=False
            )
            
            # Process data
            max_flag = calculator.receive_data(0, target_y, measured_y)
            
            logger.info(f"GDMB calculation completed for user {g.user['user_id']}")
            
            return jsonify({
                'status': 'success',
                'max_flag': max_flag,
                'target_y': target_y,
                'measured_y': measured_y,
                'parameters': {
                    'tmax': tmax,
                    'scale_tolerance': scale_tolerance,
                    'delta_tolerance': delta_tolerance
                }
            })
            
        except Exception as e:
            logger.error(f"Error in GDMB calculation: {str(e)}", exc_info=True)
            return jsonify({'error': 'Calculation failed'}), 500
    
    # Request logging middleware
    @app.before_request
    def log_request():
        """Log incoming requests."""
        user_id = getattr(g, 'user', {}).get('user_id', 'anonymous')
        logger.info(f"Request: {request.method} {request.path} from {request.remote_addr} by {user_id}")
    
    @app.after_request
    def log_response(response):
        """Log outgoing responses."""
        user_id = getattr(g, 'user', {}).get('user_id', 'anonymous')
        logger.info(f"Response: {response.status_code} for {request.method} {request.path} by {user_id}")
        return response
    
    return app


def create_development_app() -> Flask:
    """Create a development Flask app with relaxed security."""
    config = SecurityConfig(
        require_api_key=False,  # Disable auth for development
        rate_limit_requests=1000,  # Higher rate limit
        cors_origins={'*'}  # Allow all origins
    )
    
    return create_secure_app(config, auth_manager=None)
