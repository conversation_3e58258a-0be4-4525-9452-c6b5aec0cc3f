#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import os
import hashlib
import secrets
from typing import Dict, Optional, Set
from functools import wraps
from flask import request, jsonify, g
from abc import ABC, abstractmethod

from ..util.logging_config import get_logger
from ..util.exceptions import ValidationError


class AuthenticationError(ValidationError):
    """Authentication error."""
    pass


class AuthorizationError(ValidationError):
    """Authorization error."""
    pass


class AuthProvider(ABC):
    """Abstract authentication provider."""
    
    @abstractmethod
    def authenticate(self, request) -> Optional[Dict]:
        """Authenticate request and return user info if successful."""
        pass
    
    @abstractmethod
    def authorize(self, user_info: Dict, resource: str, action: str) -> bool:
        """Check if user is authorized for the given resource and action."""
        pass


class APIKeyAuth(AuthProvider):
    """API Key authentication provider."""
    
    def __init__(self, api_keys: Optional[Dict[str, Dict]] = None):
        """Initialize with API keys.
        
        Args:
            api_keys: Dict mapping API key to user info
                     Format: {api_key: {'user_id': str, 'permissions': set}}
        """
        self.logger = get_logger(__name__)
        self._api_keys = api_keys or {}
        
        # Load API keys from environment if not provided
        if not self._api_keys:
            self._load_api_keys_from_env()
    
    def _load_api_keys_from_env(self):
        """Load API keys from environment variables."""
        # Format: PXL_API_KEY_<name>=<key>:<permissions>
        for key, value in os.environ.items():
            if key.startswith('PXL_API_KEY_'):
                name = key[12:]  # Remove 'PXL_API_KEY_' prefix
                try:
                    api_key, permissions_str = value.split(':', 1)
                    permissions = set(permissions_str.split(',')) if permissions_str else set()
                    self._api_keys[api_key] = {
                        'user_id': name,
                        'permissions': permissions
                    }
                except ValueError:
                    self.logger.warning(f"Invalid API key format in {key}")
    
    def add_api_key(self, api_key: str, user_id: str, permissions: Set[str] = None):
        """Add an API key."""
        self._api_keys[api_key] = {
            'user_id': user_id,
            'permissions': permissions or set()
        }
    
    def remove_api_key(self, api_key: str):
        """Remove an API key."""
        if api_key in self._api_keys:
            del self._api_keys[api_key]
    
    def authenticate(self, request) -> Optional[Dict]:
        """Authenticate request using API key."""
        # Try different header names
        api_key = (request.headers.get('X-API-Key') or 
                  request.headers.get('Authorization', '').replace('Bearer ', '') or
                  request.args.get('api_key'))
        
        if not api_key:
            return None
        
        # Hash the API key for comparison (if stored hashed)
        user_info = self._api_keys.get(api_key)
        if user_info:
            self.logger.info(f"Authenticated user: {user_info['user_id']}")
            return user_info
        
        self.logger.warning(f"Invalid API key from {request.remote_addr}")
        return None
    
    def authorize(self, user_info: Dict, resource: str, action: str) -> bool:
        """Check if user is authorized."""
        permissions = user_info.get('permissions', set())
        
        # Check for specific permission
        required_permission = f"{resource}:{action}"
        if required_permission in permissions:
            return True
        
        # Check for wildcard permissions
        if f"{resource}:*" in permissions or "*:*" in permissions:
            return True
        
        return False
    
    @staticmethod
    def generate_api_key() -> str:
        """Generate a new API key."""
        return secrets.token_urlsafe(32)


class AuthManager:
    """Authentication and authorization manager."""
    
    def __init__(self, auth_provider: AuthProvider):
        self.auth_provider = auth_provider
        self.logger = get_logger(__name__)
    
    def require_auth(self, resource: str = 'api', action: str = 'access'):
        """Decorator to require authentication and authorization."""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # Authenticate
                user_info = self.auth_provider.authenticate(request)
                if not user_info:
                    self.logger.warning(f"Unauthenticated request to {request.endpoint} from {request.remote_addr}")
                    raise AuthenticationError("Authentication required")
                
                # Authorize
                if not self.auth_provider.authorize(user_info, resource, action):
                    self.logger.warning(f"Unauthorized request to {request.endpoint} by {user_info.get('user_id')}")
                    raise AuthorizationError("Insufficient permissions")
                
                # Store user info in request context
                g.user = user_info
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def optional_auth(self):
        """Decorator for optional authentication."""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # Try to authenticate, but don't require it
                user_info = self.auth_provider.authenticate(request)
                g.user = user_info
                return f(*args, **kwargs)
            return decorated_function
        return decorator


def handle_auth_errors(f):
    """Decorator to handle authentication and authorization errors."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except AuthenticationError as e:
            logger = get_logger(__name__)
            logger.warning(f"Authentication error from {request.remote_addr}: {str(e)}")
            return jsonify({'error': 'Authentication required'}), 401
        except AuthorizationError as e:
            logger = get_logger(__name__)
            logger.warning(f"Authorization error from {request.remote_addr}: {str(e)}")
            return jsonify({'error': 'Insufficient permissions'}), 403
    
    return decorated_function


# Default API keys for development (should be overridden in production)
DEFAULT_API_KEYS = {
    'dev-key-12345': {
        'user_id': 'developer',
        'permissions': {'*:*'}  # Full access for development
    }
}
