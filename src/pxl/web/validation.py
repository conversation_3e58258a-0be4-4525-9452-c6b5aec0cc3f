#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import re
from typing import Any, Dict, List, Optional, Union, Callable
from functools import wraps
from flask import request, jsonify
import numpy as np

from ..util.logging_config import get_logger
from ..util.exceptions import ValidationError as BaseValidationError


class ValidationError(BaseValidationError):
    """Validation error for web requests."""
    pass


class RequestValidator:
    """Request validation utilities."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    @staticmethod
    def validate_json_request(required_fields: Optional[List[str]] = None,
                            optional_fields: Optional[List[str]] = None,
                            field_validators: Optional[Dict[str, Callable]] = None):
        """Decorator to validate JSON request data.
        
        Args:
            required_fields: List of required field names
            optional_fields: List of optional field names
            field_validators: Dict mapping field names to validator functions
        """
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                validator = RequestValidator()
                
                # Check content type
                if not request.is_json:
                    raise ValidationError("Content-Type must be application/json")
                
                try:
                    data = request.get_json()
                except Exception as e:
                    raise ValidationError(f"Invalid JSON: {str(e)}")
                
                if data is None:
                    raise ValidationError("Request body must contain valid JSON")
                
                # Validate required fields
                if required_fields:
                    for field in required_fields:
                        if field not in data:
                            raise ValidationError(f"Missing required field: {field}")
                
                # Validate field types and values
                if field_validators:
                    for field, validator_func in field_validators.items():
                        if field in data:
                            try:
                                validator_func(data[field])
                            except Exception as e:
                                raise ValidationError(f"Invalid value for field '{field}': {str(e)}")
                
                # Add validated data to request context
                request.validated_data = data
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    @staticmethod
    def validate_query_params(param_validators: Dict[str, Callable]):
        """Decorator to validate query parameters.
        
        Args:
            param_validators: Dict mapping parameter names to validator functions
        """
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                for param, validator_func in param_validators.items():
                    value = request.args.get(param)
                    if value is not None:
                        try:
                            validator_func(value)
                        except Exception as e:
                            raise ValidationError(f"Invalid query parameter '{param}': {str(e)}")
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    # Field validators
    @staticmethod
    def validate_string(value: Any, min_length: int = 0, max_length: int = 1000, 
                       pattern: Optional[str] = None) -> None:
        """Validate string field."""
        if not isinstance(value, str):
            raise ValueError("Must be a string")
        
        if len(value) < min_length:
            raise ValueError(f"Must be at least {min_length} characters long")
        
        if len(value) > max_length:
            raise ValueError(f"Must be at most {max_length} characters long")
        
        if pattern and not re.match(pattern, value):
            raise ValueError(f"Must match pattern: {pattern}")
    
    @staticmethod
    def validate_number(value: Any, min_val: Optional[float] = None, 
                       max_val: Optional[float] = None) -> None:
        """Validate numeric field."""
        if not isinstance(value, (int, float)):
            raise ValueError("Must be a number")
        
        if min_val is not None and value < min_val:
            raise ValueError(f"Must be at least {min_val}")
        
        if max_val is not None and value > max_val:
            raise ValueError(f"Must be at most {max_val}")
    
    @staticmethod
    def validate_array(value: Any, min_length: int = 0, max_length: int = 1000,
                      item_validator: Optional[Callable] = None) -> None:
        """Validate array field."""
        if not isinstance(value, list):
            raise ValueError("Must be an array")
        
        if len(value) < min_length:
            raise ValueError(f"Must have at least {min_length} items")
        
        if len(value) > max_length:
            raise ValueError(f"Must have at most {max_length} items")
        
        if item_validator:
            for i, item in enumerate(value):
                try:
                    item_validator(item)
                except Exception as e:
                    raise ValueError(f"Item at index {i}: {str(e)}")
    
    @staticmethod
    def validate_xyz_array(value: Any) -> None:
        """Validate XYZ color array."""
        if not isinstance(value, list):
            raise ValueError("Must be an array")
        
        if len(value) != 3:
            raise ValueError("XYZ array must have exactly 3 elements")
        
        for i, component in enumerate(value):
            if not isinstance(component, (int, float)):
                raise ValueError(f"XYZ component {i} must be a number")
            
            if component < 0:
                raise ValueError(f"XYZ component {i} must be non-negative")
            
            if component > 1000:  # Reasonable upper bound
                raise ValueError(f"XYZ component {i} seems too large (>{1000})")
    
    @staticmethod
    def validate_de_threshold(value: Any) -> None:
        """Validate Delta E threshold array."""
        if not isinstance(value, list):
            raise ValueError("Must be an array")
        
        if len(value) != 3:
            raise ValueError("Delta E threshold must have exactly 3 elements")
        
        for i, threshold in enumerate(value):
            if not isinstance(threshold, (int, float)):
                raise ValueError(f"Threshold {i} must be a number")
            
            if threshold < 0:
                raise ValueError(f"Threshold {i} must be non-negative")
            
            # Allow infinity for the last threshold
            if i < 2 and threshold > 100:
                raise ValueError(f"Threshold {i} seems too large (>100)")
    
    @staticmethod
    def validate_enum_value(value: Any, allowed_values: List[str]) -> None:
        """Validate enum field."""
        if not isinstance(value, str):
            raise ValueError("Must be a string")
        
        if value not in allowed_values:
            raise ValueError(f"Must be one of: {', '.join(allowed_values)}")
    
    @staticmethod
    def validate_boolean(value: Any) -> None:
        """Validate boolean field."""
        if not isinstance(value, bool):
            raise ValueError("Must be a boolean")
    
    @staticmethod
    def validate_file_path(value: Any) -> None:
        """Validate file path (basic security check)."""
        if not isinstance(value, str):
            raise ValueError("Must be a string")
        
        # Basic path traversal protection
        if '..' in value or value.startswith('/'):
            raise ValueError("Invalid file path")
        
        # Check for reasonable length
        if len(value) > 255:
            raise ValueError("File path too long")


def handle_validation_errors(f):
    """Decorator to handle validation errors."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValidationError as e:
            logger = get_logger(__name__)
            logger.warning(f"Validation error from {request.remote_addr}: {str(e)}")
            return jsonify({'error': str(e)}), 400
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Unexpected error in validation: {str(e)}", exc_info=True)
            return jsonify({'error': 'Internal server error'}), 500
    
    return decorated_function
