#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import os
import ssl
import logging
from typing import Optional
from werkzeug.serving import WSGI<PERSON><PERSON><PERSON><PERSON><PERSON>ler

from .api import create_secure_app, create_development_app
from .security import SecurityConfig
from .auth import AuthManager, APIKeyAuth
from ..util.logging_config import get_logger


class SecureWSGIRequestHandler(WSGIRequestHandler):
    """Custom WSGI request handler with security enhancements."""
    
    def log_request(self, code='-', size='-'):
        """Override to use our custom logger."""
        logger = get_logger('werkzeug')
        logger.info(f'{self.address_string()} - - [{self.log_date_time_string()}] '
                   f'"{self.requestline}" {code} {size}')


class PXLWebServer:
    """Secure web server for PXL API."""
    
    def __init__(self, 
                 host: str = '127.0.0.1',
                 port: int = 5000,
                 debug: bool = False,
                 ssl_context: Optional[ssl.SSLContext] = None,
                 security_config: Optional[SecurityConfig] = None,
                 auth_manager: Optional[AuthManager] = None):
        """Initialize the web server.
        
        Args:
            host: Host to bind to
            port: Port to bind to
            debug: Enable debug mode
            ssl_context: SSL context for HTTPS
            security_config: Security configuration
            auth_manager: Authentication manager
        """
        self.host = host
        self.port = port
        self.debug = debug
        self.ssl_context = ssl_context
        self.logger = get_logger(__name__)
        
        # Create Flask app
        if debug:
            self.app = create_development_app()
            self.logger.warning("Running in development mode with relaxed security!")
        else:
            self.app = create_secure_app(security_config, auth_manager)
        
        # Configure Flask app
        self._configure_app()
    
    def _configure_app(self):
        """Configure Flask application."""
        # Security settings
        self.app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', 
                                                      os.urandom(32).hex())
        
        # Disable debug in production
        if not self.debug:
            self.app.config['DEBUG'] = False
            self.app.config['TESTING'] = False
        
        # Configure logging
        if not self.debug:
            # Disable Flask's default logging in production
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.WARNING)
    
    def run(self, threaded: bool = True, processes: int = 1):
        """Run the web server.
        
        Args:
            threaded: Enable threading
            processes: Number of processes (if not threaded)
        """
        try:
            self.logger.info(f"Starting PXL Web Server on {self.host}:{self.port}")
            
            if self.ssl_context:
                self.logger.info("HTTPS enabled")
            
            # Run the server
            self.app.run(
                host=self.host,
                port=self.port,
                debug=self.debug,
                ssl_context=self.ssl_context,
                threaded=threaded,
                processes=processes,
                request_handler=SecureWSGIRequestHandler,
                use_reloader=False  # Disable reloader for security
            )
            
        except Exception as e:
            self.logger.error(f"Failed to start web server: {e}", exc_info=True)
            raise
    
    @classmethod
    def create_production_server(cls, 
                                host: str = '0.0.0.0',
                                port: int = 8443,
                                cert_file: Optional[str] = None,
                                key_file: Optional[str] = None) -> 'PXLWebServer':
        """Create a production-ready server with HTTPS.
        
        Args:
            host: Host to bind to
            port: Port to bind to
            cert_file: SSL certificate file path
            key_file: SSL private key file path
            
        Returns:
            Configured production server
        """
        # Setup SSL context
        ssl_context = None
        if cert_file and key_file:
            ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
            ssl_context.load_cert_chain(cert_file, key_file)
            
            # Security settings
            ssl_context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
            ssl_context.options |= ssl.OP_NO_SSLv2
            ssl_context.options |= ssl.OP_NO_SSLv3
            ssl_context.options |= ssl.OP_NO_TLSv1
            ssl_context.options |= ssl.OP_NO_TLSv1_1
        
        # Production security config
        security_config = SecurityConfig(
            require_api_key=True,
            rate_limit_requests=100,
            rate_limit_window=3600,
            cors_origins=set(),  # No CORS in production by default
            enable_security_headers=True
        )
        
        # Production auth manager
        api_key_auth = APIKeyAuth()  # Will load from environment
        auth_manager = AuthManager(api_key_auth)
        
        return cls(
            host=host,
            port=port,
            debug=False,
            ssl_context=ssl_context,
            security_config=security_config,
            auth_manager=auth_manager
        )
    
    @classmethod
    def create_development_server(cls, 
                                 host: str = '127.0.0.1',
                                 port: int = 5000) -> 'PXLWebServer':
        """Create a development server with relaxed security.
        
        Args:
            host: Host to bind to
            port: Port to bind to
            
        Returns:
            Configured development server
        """
        return cls(
            host=host,
            port=port,
            debug=True
        )


def main():
    """Main entry point for the web server."""
    import argparse
    
    parser = argparse.ArgumentParser(description='PXL Web Server')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--production', action='store_true', help='Run in production mode')
    parser.add_argument('--cert', help='SSL certificate file')
    parser.add_argument('--key', help='SSL private key file')
    
    args = parser.parse_args()
    
    if args.production:
        server = PXLWebServer.create_production_server(
            host=args.host,
            port=args.port,
            cert_file=args.cert,
            key_file=args.key
        )
    else:
        server = PXLWebServer.create_development_server(
            host=args.host,
            port=args.port
        )
    
    try:
        server.run()
    except KeyboardInterrupt:
        print("\nShutting down server...")


if __name__ == '__main__':
    main()
