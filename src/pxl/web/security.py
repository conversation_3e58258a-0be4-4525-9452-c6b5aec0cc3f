#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import os
import time
from dataclasses import dataclass
from typing import Dict, Optional, Set
from flask import Flask, request, jsonify, g
from functools import wraps
import secrets

from ..util.logging_config import get_logger
from ..util.exceptions import ValidationError


@dataclass
class SecurityConfig:
    """Security configuration for the web API."""
    
    # Rate limiting
    rate_limit_requests: int = 100  # requests per window
    rate_limit_window: int = 3600   # window in seconds (1 hour)
    
    # Authentication
    require_api_key: bool = True
    api_key_header: str = 'X-API-Key'
    
    # CORS
    cors_origins: Set[str] = None
    cors_methods: Set[str] = None
    cors_headers: Set[str] = None
    
    # Security headers
    enable_security_headers: bool = True
    
    # Request size limits
    max_content_length: int = 16 * 1024 * 1024  # 16MB
    
    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = {'http://localhost:3000', 'http://127.0.0.1:3000'}
        if self.cors_methods is None:
            self.cors_methods = {'GET', 'POST', 'OPTIONS'}
        if self.cors_headers is None:
            self.cors_headers = {'Content-Type', 'Authorization', 'X-API-Key'}


class SecurityMiddleware:
    """Security middleware for Flask applications."""
    
    def __init__(self, app: Flask, config: SecurityConfig):
        self.app = app
        self.config = config
        self.logger = get_logger(__name__)
        
        # Rate limiting storage (in production, use Redis or similar)
        self._rate_limit_storage: Dict[str, Dict] = {}
        
        # Setup middleware
        self._setup_security_headers()
        self._setup_cors()
        self._setup_request_limits()
        self._setup_error_handlers()
    
    def _setup_security_headers(self):
        """Setup security headers."""
        if not self.config.enable_security_headers:
            return
        
        @self.app.after_request
        def add_security_headers(response):
            # Prevent clickjacking
            response.headers['X-Frame-Options'] = 'DENY'
            
            # Prevent MIME type sniffing
            response.headers['X-Content-Type-Options'] = 'nosniff'
            
            # XSS protection
            response.headers['X-XSS-Protection'] = '1; mode=block'
            
            # Referrer policy
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            
            # Content Security Policy
            response.headers['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data:; "
                "connect-src 'self'"
            )
            
            # HSTS (only for HTTPS)
            if request.is_secure:
                response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            
            return response
    
    def _setup_cors(self):
        """Setup CORS headers."""
        @self.app.after_request
        def add_cors_headers(response):
            origin = request.headers.get('Origin')
            
            # Check if origin is allowed
            if origin in self.config.cors_origins or '*' in self.config.cors_origins:
                response.headers['Access-Control-Allow-Origin'] = origin
                response.headers['Access-Control-Allow-Methods'] = ', '.join(self.config.cors_methods)
                response.headers['Access-Control-Allow-Headers'] = ', '.join(self.config.cors_headers)
                response.headers['Access-Control-Max-Age'] = '3600'
            
            return response
        
        # Handle preflight requests
        @self.app.before_request
        def handle_preflight():
            if request.method == 'OPTIONS':
                response = jsonify({'status': 'ok'})
                return response
    
    def _setup_request_limits(self):
        """Setup request size limits."""
        self.app.config['MAX_CONTENT_LENGTH'] = self.config.max_content_length
    
    def _setup_error_handlers(self):
        """Setup secure error handlers."""
        @self.app.errorhandler(400)
        def bad_request(error):
            self.logger.warning(f"Bad request from {request.remote_addr}: {error}")
            return jsonify({'error': 'Bad request'}), 400
        
        @self.app.errorhandler(401)
        def unauthorized(error):
            self.logger.warning(f"Unauthorized request from {request.remote_addr}")
            return jsonify({'error': 'Unauthorized'}), 401
        
        @self.app.errorhandler(403)
        def forbidden(error):
            self.logger.warning(f"Forbidden request from {request.remote_addr}")
            return jsonify({'error': 'Forbidden'}), 403
        
        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({'error': 'Not found'}), 404
        
        @self.app.errorhandler(413)
        def request_entity_too_large(error):
            self.logger.warning(f"Request too large from {request.remote_addr}")
            return jsonify({'error': 'Request entity too large'}), 413
        
        @self.app.errorhandler(429)
        def rate_limit_exceeded(error):
            self.logger.warning(f"Rate limit exceeded for {request.remote_addr}")
            return jsonify({'error': 'Rate limit exceeded'}), 429
        
        @self.app.errorhandler(500)
        def internal_error(error):
            self.logger.error(f"Internal server error: {error}", exc_info=True)
            return jsonify({'error': 'Internal server error'}), 500
    
    def rate_limit(self, requests_per_window: Optional[int] = None, window_seconds: Optional[int] = None):
        """Rate limiting decorator."""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # Use provided limits or defaults
                limit = requests_per_window or self.config.rate_limit_requests
                window = window_seconds or self.config.rate_limit_window
                
                # Get client identifier
                client_id = self._get_client_id()
                
                # Check rate limit
                if self._is_rate_limited(client_id, limit, window):
                    self.logger.warning(f"Rate limit exceeded for client {client_id}")
                    return jsonify({'error': 'Rate limit exceeded'}), 429
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def _get_client_id(self) -> str:
        """Get client identifier for rate limiting."""
        # In production, consider using a more sophisticated method
        # that takes into account authenticated users, API keys, etc.
        return request.remote_addr or 'unknown'
    
    def _is_rate_limited(self, client_id: str, limit: int, window: int) -> bool:
        """Check if client is rate limited."""
        now = time.time()
        
        # Clean old entries
        self._cleanup_rate_limit_storage(now, window)
        
        # Get client's request history
        if client_id not in self._rate_limit_storage:
            self._rate_limit_storage[client_id] = {'requests': [], 'count': 0}
        
        client_data = self._rate_limit_storage[client_id]
        
        # Remove old requests outside the window
        client_data['requests'] = [req_time for req_time in client_data['requests'] 
                                  if now - req_time < window]
        
        # Check if limit exceeded
        if len(client_data['requests']) >= limit:
            return True
        
        # Add current request
        client_data['requests'].append(now)
        return False
    
    def _cleanup_rate_limit_storage(self, now: float, window: int):
        """Clean up old rate limit entries."""
        # Remove clients with no recent requests
        clients_to_remove = []
        for client_id, data in self._rate_limit_storage.items():
            if not data['requests'] or now - max(data['requests']) > window * 2:
                clients_to_remove.append(client_id)
        
        for client_id in clients_to_remove:
            del self._rate_limit_storage[client_id]


def generate_api_key() -> str:
    """Generate a secure API key."""
    return secrets.token_urlsafe(32)
