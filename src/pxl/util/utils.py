#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Oct 21, 2020
"""

import os
import sys
import time


def raise_without_traceback(exc, msg):
    sys.stderr.write(f"{exc.__name__}: {msg}")


def file_was_recently_updated(file_path, seconds=60):
    """
    Check if the file at 'file_path' was updated in the last 'seconds'.
    """
    # Get the last modified time of the file
    last_modified_time = os.path.getmtime(file_path)

    # Get the current time
    current_time = time.time()

    # Calculate the time difference
    time_difference = current_time - last_modified_time

    # Return True if the file was modified within the specified number of seconds
    return time_difference < seconds
