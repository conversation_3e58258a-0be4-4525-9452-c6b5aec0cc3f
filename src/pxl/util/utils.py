#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Oct 21, 2020
"""

import os
import sys
import time
import warnings
from typing import Type

from .logging_config import get_logger
from .exceptions import PXLException


def raise_without_traceback(exc: Type[Exception], msg: str):
    """
    Legacy function for raising exceptions without traceback.

    DEPRECATED: This function is deprecated and will be removed in a future version.
    Use the new exception handling system in pxl.util.error_handling instead.

    Args:
        exc: Exception class
        msg: Error message
    """
    warnings.warn(
        "raise_without_traceback is deprecated. Use pxl.util.error_handling.log_and_raise instead.",
        DeprecationWarning,
        stacklevel=2
    )

    logger = get_logger(__name__)
    logger.error(f"{exc.__name__}: {msg}")

    # For backward compatibility, still raise the exception
    # but now with proper logging
    raise exc(msg)


def file_was_recently_updated(file_path, seconds=60):
    """
    Check if the file at 'file_path' was updated in the last 'seconds'.
    """
    # Get the last modified time of the file
    last_modified_time = os.path.getmtime(file_path)

    # Get the current time
    current_time = time.time()

    # Calculate the time difference
    time_difference = current_time - last_modified_time

    # Return True if the file was modified within the specified number of seconds
    return time_difference < seconds
