#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""


class PXLException(Exception):
    """Base exception class for PXL project."""
    
    def __init__(self, message: str, details: str = None):
        self.message = message
        self.details = details
        super().__init__(self.message)
    
    def __str__(self):
        if self.details:
            return f"{self.message}. Details: {self.details}"
        return self.message


class CalculationError(PXLException):
    """Exception raised for errors in calculation operations."""
    pass


class DataProcessingError(PXLException):
    """Exception raised for errors in data processing operations."""
    pass


class FileOperationError(PXLException):
    """Exception raised for file operation errors."""
    pass


class ConfigurationError(PXLException):
    """Exception raised for configuration-related errors."""
    pass


class PlotRenderingError(PXLException):
    """Exception raised for plotting and rendering errors."""
    pass


class ThreadingError(PXLException):
    """Exception raised for threading-related errors."""
    pass


class ValidationError(PXLException):
    """Exception raised for data validation errors."""
    pass
