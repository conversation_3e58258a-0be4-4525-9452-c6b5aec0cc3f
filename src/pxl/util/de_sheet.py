#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Jun 3, 2024
"""

import numpy as np
import pandas as pd


class DeltaE2kCalculatorExcelSheet:
    def __init__(self, file_path):
        self.file_path = file_path
        self.raw_data = None
        self.expected_values = None
        self.delta_e2000 = None
        self.load_data()

    def load_data(self):
        """Load data from the Excel file."""
        try:
            xls = pd.ExcelFile(self.file_path)
            print(f"{self.__class__.__name__}: Excel file loaded successfully. file={self.file_path}")

            sheet_names = xls.sheet_names
            print(f"{self.__class__.__name__}: Sheet names:", sheet_names)

            # Load Raw Data sheet
            if 'Raw Data' in sheet_names:
                self.raw_data = pd.read_excel(xls, 'Raw Data')
                print(f"{self.__class__.__name__}: Sheet 'Raw Data' loaded successfully.")
            else:
                print(f"{self.__class__.__name__}: Sheet 'Raw Data' not found.")

            # Load Expected_Values sheet
            if 'Expected_Values' in sheet_names:
                self.expected_values = pd.read_excel(xls, 'Expected_Values')
                print(f"{self.__class__.__name__}: Sheet 'Expected_Values' loaded successfully.")
            else:
                print(f"{self.__class__.__name__}: Sheet 'Expected_Values' not found.")

            # Load DeltaE2000 sheet
            if 'DeltaE2000' in sheet_names:
                self.delta_e2000 = pd.read_excel(xls, 'DeltaE2000', header=None)
                print(f"{self.__class__.__name__}: Sheet 'DeltaE2000' loaded successfully.")
            else:
                print(f"{self.__class__.__name__}: Sheet 'DeltaE2000' not found.")
        except Exception as e:
            print(f"{self.__class__.__name__}: Error loading data: {e}, file={self.file_path}")

    def get_measurement_xyz(self) -> np.ndarray:
        """Get XYZ values from Raw Data."""
        if self.raw_data is not None:
            measurement_xyz = self.raw_data[['Measurment data', 'Unnamed: 5', 'Unnamed: 6']]
            measurement_xyz.columns = ['X', 'Y', 'Z']
            measurement_xyz = measurement_xyz.iloc[1:]  # Skip the title row
            return np.array([np.array(row, dtype=np.float64) for row in measurement_xyz.to_numpy()])
        else:
            print(f"{self.__class__.__name__}: Raw Data is not loaded.")
            return None

    def get_expected_xyz(self) -> np.ndarray:
        """Get Expected XYZ values from Expected_Values."""
        if self.expected_values is not None:
            # Print columns to check actual column names
            expected_xyz = self.expected_values.iloc[1:, 1:4]
            expected_xyz.columns = ['X', 'Y', 'Z']
            return np.array([np.array(row, dtype=np.float64) for row in expected_xyz.to_numpy()])
        else:
            print(f"{self.__class__.__name__}: Expected Values are not loaded.")
            return None

    def get_delta_e2000(self) -> np.ndarray:
        """Get DE2k values from DeltaE2000."""
        if self.delta_e2000 is not None:
            try:
                # Search for the 'DE2k' keyword in the entire dataframe
                de2k_values = []
                for idx, row in self.delta_e2000.iterrows():
                    for col_name, val in row.items():
                        if isinstance(val, str) and 'DE2k' in val:
                            # Search for float values in the same column in the following rows
                            for i in range(idx + 1, len(self.delta_e2000)):
                                next_val = self.delta_e2000.at[i, col_name]
                                if isinstance(next_val, (int, float)) and not pd.isna(next_val):
                                    de2k_values.append(next_val)

                if de2k_values:
                    return np.array(de2k_values)
                else:
                    print(f"{self.__class__.__name__}: DE2k column not found.")
                    return None
            except Exception as e:
                print(f"{self.__class__.__name__}: Error getting delta E2000: {e}")
                return None
        else:
            print(f"{self.__class__.__name__}: DeltaE2000 is not loaded.")
            return None

    def get_reference_white_xyz(self) -> np.ndarray:
        """Get reference white XYZ values from DeltaE2000."""
        if self.delta_e2000 is not None:
            try:
                x = self.delta_e2000.iloc[3, 2]  # C4
                y = self.delta_e2000.iloc[3, 3]  # D4
                z = self.delta_e2000.iloc[3, 4]  # E4

                print(f"{self.__class__.__name__}: Read values from C4, D4, E4: x={x}, y={y}, z={z}")

                if pd.isna(x) or pd.isna(y) or pd.isna(z):
                    print(f"{self.__class__.__name__}: One or more values are NaN, "
                          f"please check the cell references and data.")

                reference_white_xyz = np.array([x, y, z])
                print(f"{self.__class__.__name__}: Reference White XYZ values: {reference_white_xyz}")
                return reference_white_xyz
            except Exception as e:
                print(f"{self.__class__.__name__}: Error getting reference white XYZ: {e}")
                return None
        else:
            print(f"{self.__class__.__name__}: DeltaE2000 is not loaded.")
            return None

    def get_scale_factor(self) -> float:
        if self.delta_e2000 is not None:
            try:
                f = self.delta_e2000.iloc[3, 9]  # J4

                print(f"{self.__class__.__name__}: Read values from J4: f={f}")

                if pd.isna(f):
                    print(f"{self.__class__.__name__}: The value is NaN, please check the cell references and data.")

                print(f"{self.__class__.__name__}: Scale factor value: {f}")
                return f
            except Exception as e:
                print(f"{self.__class__.__name__}: Error getting scale factor: {e}")
                return 1.0
        else:
            print(f"{self.__class__.__name__}: DeltaE2000 is not loaded.")
            return 1.0


if __name__ == "__main__":
    file_path = ('../../../test/test_sheets/'
                 'FFALCON_Unit1_HDMI_Sink-led_ext_dark_Extend_DM4_ExtendedColors_DeltaE2k_Calculator_v2.3.xlsx')
    calculator = DeltaE2kCalculatorExcelSheet(file_path)

    measurement_xyz = calculator.get_measurement_xyz()
    expected_xyz = calculator.get_expected_xyz()
    delta_e2000 = calculator.get_delta_e2000()
    reference_white = calculator.get_reference_white_xyz()
    scale_factor = calculator.get_scale_factor()

    print(f"Measurement XYZ: {measurement_xyz}")
    print(f"\nExpected XYZ: {expected_xyz}")
    print(f"\nDelta E2000: {delta_e2000}")
    print(f"\nReference White: {reference_white}")
    print(f"\nScale Factor: {scale_factor}")
