#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Jan 21, 2024
"""

import time
from functools import wraps

from pxl.util.thread import running_in_main_thread


def ensure_fixed_interval(default_interval: float = 1.0):
    """
    Decorator to ensure a function runs at a fixed interval, sleeping for the remaining time if the
    function finishes early.

    Args:
        default_interval (float): The base interval between executions. Defaults to 1.0 seconds.
        interval (float): The exact interval to use. If not specified, uses default_interval.
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # extract interval from kwargs, use default if not specified
            interval = kwargs.pop('interval', default_interval)

            start_time = time.time()
            result = func(*args, **kwargs)  # call the original function
            elapsed_time = time.time() - start_time

            # calculate the sleep time
            sleep_time = max(0.0, interval - elapsed_time) if interval else 0.0
            if sleep_time > 0.0:
                # print(f"sleep time: {sleep_time}")
                time.sleep(sleep_time)

            return result

        return wrapper

    return decorator


def main_thread_required(func):
    """
    Decorator to ensure a function runs in the main thread only.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        running_in_main_thread(func)
        return func(*args, **kwargs)
    return wrapper
