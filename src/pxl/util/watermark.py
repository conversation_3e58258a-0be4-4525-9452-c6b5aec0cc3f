#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: May 21, 2024
"""

import base64
import os
from io import BytesIO

import matplotlib.image as mpimg


def gen_watermark_base64(image_path):
    with open(image_path, 'rb') as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('ascii')
    print(f"encoded_string = {encoded_string}")


def add_watermark2(fig, watermark_path, alpha=0.02, size=(0.2, 0.2)):
    """
    Adds a watermark to a Matplotlib figure by repeating the watermark across the figure.
    Parameters:
        fig (Figure): The figure object.
        watermark_path (str): Path to the watermark image.
        alpha (float): Transparency of the watermark.
        size (tuple): A tuple (width, height) specifying the relative size of each watermark instance.
    """
    if watermark_path is not None:
        if os.path.exists(watermark_path):
            # Read the watermark image
            watermark = mpimg.imread(watermark_path)
        elif isinstance(watermark_path, str):
            # Decode the watermark from a BASE64 encoded string
            watermark = mpimg.imread(BytesIO(base64.b64decode(watermark_path)), format='PNG')

        # Calculate number of tiles needed
        num_x = int(1.0 / size[0])
        num_y = int(1.0 / size[1])

        # Add watermark tiles across the figure
        for i in range(num_x):
            for j in range(num_y):
                ax_watermark = fig.add_axes([i * size[0], j * size[1], size[0], size[1]], zorder=1)
                ax_watermark.imshow(watermark, alpha=alpha)
                ax_watermark.axis('off')  # Hide axes
