#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import functools
import logging
from typing import Callable, Type, Union, Optional, Any

from .exceptions import PXLException
from .logging_config import get_logger


def handle_exceptions(
    exception_types: Union[Type[Exception], tuple] = Exception,
    logger: Optional[logging.Logger] = None,
    reraise: bool = True,
    default_return: Any = None,
    custom_message: str = None
):
    """Decorator to handle exceptions in a consistent way.
    
    Args:
        exception_types: Exception type(s) to catch
        logger: Logger instance to use (if None, creates one based on function module)
        reraise: Whether to reraise the exception after logging
        default_return: Default value to return if exception is caught and not reraised
        custom_message: Custom message to log instead of the exception message
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get logger if not provided
            nonlocal logger
            if logger is None:
                logger = get_logger(func.__module__)
            
            try:
                return func(*args, **kwargs)
            except exception_types as e:
                # Log the exception
                message = custom_message or f"Error in {func.__name__}: {str(e)}"
                logger.error(message, exc_info=True)
                
                # Reraise or return default
                if reraise:
                    raise
                else:
                    return default_return
        
        return wrapper
    return decorator


def log_and_raise(
    exception_class: Type[PXLException],
    message: str,
    details: str = None,
    logger: Optional[logging.Logger] = None,
    original_exception: Exception = None
):
    """Log an error and raise a custom exception.
    
    Args:
        exception_class: The PXL exception class to raise
        message: Error message
        details: Additional details
        logger: Logger instance
        original_exception: Original exception that caused this error
    """
    if logger is None:
        logger = get_logger(__name__)
    
    # Create full message
    full_message = message
    if details:
        full_message += f". Details: {details}"
    
    # Log the error
    if original_exception:
        logger.error(f"{full_message}. Original error: {str(original_exception)}", exc_info=True)
    else:
        logger.error(full_message)
    
    # Raise the custom exception
    raise exception_class(message, details)


def safe_execute(
    func: Callable,
    *args,
    exception_types: Union[Type[Exception], tuple] = Exception,
    logger: Optional[logging.Logger] = None,
    default_return: Any = None,
    error_message: str = None,
    **kwargs
) -> Any:
    """Safely execute a function with exception handling.
    
    Args:
        func: Function to execute
        *args: Positional arguments for the function
        exception_types: Exception types to catch
        logger: Logger instance
        default_return: Value to return if exception occurs
        error_message: Custom error message
        **kwargs: Keyword arguments for the function
        
    Returns:
        Function result or default_return if exception occurs
    """
    if logger is None:
        logger = get_logger(__name__)
    
    try:
        return func(*args, **kwargs)
    except exception_types as e:
        message = error_message or f"Error executing {func.__name__}: {str(e)}"
        logger.error(message, exc_info=True)
        return default_return
