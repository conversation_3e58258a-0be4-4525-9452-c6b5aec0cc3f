#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import logging
import os
import sys
from logging.handlers import RotatingFileHandler
from typing import Optional


class PXLLogger:
    """Centralized logging configuration for PXL project."""
    
    _loggers = {}
    _configured = False
    
    @classmethod
    def get_logger(cls, name: str, level: int = logging.INFO) -> logging.Logger:
        """Get a configured logger instance.
        
        Args:
            name: Logger name (typically module name)
            level: Logging level
            
        Returns:
            Configured logger instance
        """
        if name not in cls._loggers:
            cls._loggers[name] = cls._create_logger(name, level)
        return cls._loggers[name]
    
    @classmethod
    def _create_logger(cls, name: str, level: int) -> logging.Logger:
        """Create and configure a logger instance."""
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # Avoid duplicate handlers
        if logger.handlers:
            return logger
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # File handler (optional, based on environment)
        log_file = os.environ.get('PXL_LOG_FILE', 'pxl.log')
        if log_file and log_file.lower() != 'none':
            try:
                file_handler = RotatingFileHandler(
                    log_file, maxBytes=10*1024*1024, backupCount=5
                )
                file_handler.setLevel(logging.DEBUG)
                file_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
                )
                file_handler.setFormatter(file_formatter)
                logger.addHandler(file_handler)
            except (OSError, PermissionError) as e:
                # If file logging fails, just use console logging
                logger.warning(f"Could not set up file logging: {e}")
        
        return logger
    
    @classmethod
    def configure_root_logger(cls, level: int = logging.INFO):
        """Configure the root logger for the entire application."""
        if cls._configured:
            return
        
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # Remove default handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Add our custom handler
        cls.get_logger('pxl', level)
        cls._configured = True


def get_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """Convenience function to get a logger."""
    return PXLLogger.get_logger(name, level)
