#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: November 13, 2024
"""

import numpy as np
import pandas as pd


class GDMBCalculatorExcelSheet:
    def __init__(self, file_path):
        self.file_path = file_path
        self.raw_data = None
        self.expected_values = None
        self.delta = None
        self.load_data()

    def load_data(self):
        """Load data from the Excel file."""
        try:
            xls = pd.ExcelFile(self.file_path)
            print("Excel file loaded successfully.")

            sheet_names = xls.sheet_names
            print("Sheet names:", sheet_names)

            # Load Raw Data sheet
            if 'Measurement' in sheet_names:
                self.raw_data = pd.read_excel(xls, 'Measurement')
                print("Measurement loaded successfully.")
            else:
                print("Sheet 'Measurement' not found.")

            # Load Expected_Values sheet
            if 'Luminance Target' in sheet_names:
                self.expected_values = pd.read_excel(xls, 'Luminance Target')
                print("Luminance Target loaded successfully.")
            else:
                print("Sheet 'Luminance Target' not found.")

            # Load DeltaE2000 sheet
            if 'Delta' in sheet_names:
                self.delta = pd.read_excel(xls, 'Delta', header=None)
                print("Delta loaded successfully.")
            else:
                print("Sheet 'Delta' not found.")
        except Exception as e:
            print(f"Error loading data: {e}")

    def get_measurement_y(self) -> np.ndarray:
        """Get Y values from Measurement Data."""
        if self.raw_data is not None:
            target_y = self.raw_data.iloc[4:, 1:3]
            target_y = target_y.iloc[1:]  # Skip the title row
            return np.array([np.array(row[1], dtype=np.float64) for row in target_y.to_numpy()])
        else:
            print("Raw Data is not loaded.")
            return None

    def get_target_y(self) -> np.ndarray:
        """Get Expected Y values from Target Data."""
        if self.expected_values is not None:
            # Print columns to check actual column names
            target_y = self.expected_values.iloc[4:, 1:3]
            target_y = target_y.iloc[4:]  # Skip the title row
            return np.array([np.array(row[1], dtype=np.float64) for row in target_y.to_numpy()])
        else:
            print("Target Values are not loaded.")
            return None

    def get_scaled_data(self) -> np.ndarray:
        if self.delta is not None:
            try:
                scaled_values = []
                scaled_col = self.delta.iloc[11:, 3]
                scaled_col = scaled_col.iloc[1:]
                scaled_col_np = np.asarray(scaled_col, dtype=np.float64)
                i = 0
                while not pd.isna(scaled_col_np[i]):
                    scaled_values.append(scaled_col_np[i])
                    i += 1
                if scaled_values:
                    return np.array(scaled_values)
                else:
                    print("(Scaled) Measured column not found.")
                    return None
            except Exception as e:
                print(f"Error getting delta: {e}")
                return None
        else:
            print("Delta is not loaded.")
            return None
