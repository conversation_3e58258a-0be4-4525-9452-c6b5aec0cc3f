#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 18, 2024
"""

from abc import ABC, abstractmethod
from io import BytesIO


class BytesIOPusher(ABC):
    """
    Abstract class for pushing buffer to frontend or other targets.
    """
    @abstractmethod
    def push(self, buffer: BytesIO):
        """
        Push the buffer to the frontend or other target.

        Args:
        - buffer: BytesIO object containing the image data.
        """
        pass
