#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 18, 2024
"""

import threading

from .exceptions import ThreadingError
from .logging_config import get_logger


def running_in_main_thread(func):
    """Check if the function is called from the main thread.

    Args:
        func (callable): The function to check.

    Raises:
        ThreadingError: If the function is not called from the main thread.
    """
    if threading.current_thread() != threading.main_thread():
        logger = get_logger(__name__)
        error_msg = f"Function {func.__name__} must be called from the main thread"
        logger.error(f"{error_msg}. Current thread: {threading.current_thread().name}")
        raise ThreadingError(
            error_msg,
            f"Current thread: {threading.current_thread().name}, Main thread: {threading.main_thread().name}"
        )
