#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 18, 2024
"""

import threading


def running_in_main_thread(func):
    """Check if the function is called from the main thread.

    Args:
        func (callable): The function to check.

    Raises:
        RuntimeError: If the function is not called from the main thread.
    """
    if threading.current_thread() != threading.main_thread():
        raise RuntimeError(f"This function must be called from the main thread. function={func.__name__}")
