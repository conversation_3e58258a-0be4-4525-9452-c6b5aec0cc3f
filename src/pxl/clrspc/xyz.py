#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Jan 21, 2024
"""

import numpy as np


def XYZ2xyz(XYZ):
    """
    Convert an array of XYZ color values to xyz chromaticity coordinates.
    Handles both single XYZ color value (1D array) and multiple values (2D array).

    Parameters:
    XYZ : np.array
        Array of XYZ color values. Can be 1D array [X, Y, Z] for a single color or
        2D array shape (n, 3) where each row is [X, Y, Z] for multiple colors.

    Returns:
    np.array
        Array of xyz chromaticity coordinates. Returns 1D array [x, y, z] for single color input
        or 2D array shape (n, 3) where each row is [x, y, z] for multiple colors.
    """
    # Ensure XYZ is at least 2D
    XYZ = np.atleast_2d(XYZ)

    # Sum all the values in each row (axis=1)
    sum_values = np.sum(XYZ, axis=1)

    # Avoid division by zero
    sum_values[sum_values == 0] = 1

    # Normalize XYZ by the sum to get xyz
    xyz = XYZ / sum_values[:, np.newaxis]

    # Return array in original input shape
    if XYZ.shape[0] == 1:
        return xyz[0]  # Return 1D array if original input was 1D
    return xyz


def visualize_XYZ(XYZ, max_Y=1.0):
    """
    Convert XYZ color space values to RGB for visualization, applying a linear transformation and gamma correction.

    Args:
    - XYZ (np.array): An array containing the XYZ values.
    - max_Y (float, optional): The maximum Y value for normalization, defaulting to 1.0.

    Returns:
    - np.array: The RGB color values, scaled and gamma corrected, suitable for display on typical monitors.
    """
    # Define the transformation matrix from XYZ to linear RGB
    M = np.array([
        [2.4935, -0.9316, -0.4027],
        [-0.8296, 1.7629, 0.0236],
        [0.0358, -0.0762, 0.9569]
    ])
    rgb_linear = M @ (XYZ.T / (0.8 * max_Y))

    # Apply gamma correction
    def gamma_correct(c):
        if c <= 0.0031308:
            return 12.92 * c
        else:
            return 1.055 * np.power(c, 1 / 3.0) - 0.055

    # Apply the gamma correction to each color channel
    rgb = np.vectorize(gamma_correct)(rgb_linear)

    # Ensure that all RGB values are within the range [0, 1] to be valid for display purposes
    rgb = np.clip(rgb, 0, 1)

    return rgb


def test_XYZ2xyz():
    XYZ_values = np.array([
        [321.081575, 337.818478, 367.903832],
        [19.570348, 18.321265, 16.761252],
        [67.025945, 55.2077, 26.181701]
    ])

    xyz_coordinates = XYZ2xyz(XYZ_values)
    print(xyz_coordinates)


def test_visualize_xyz():
    XYZ_values = np.array([
        [769.456, 809.565, 881.663],
    ])

    rgb = visualize_XYZ(XYZ=XYZ_values[0], max_Y=XYZ_values[0][1])
    print(f"rgb = {rgb}")


if __name__ == "__main__":
    # test code
    test_visualize_xyz()
