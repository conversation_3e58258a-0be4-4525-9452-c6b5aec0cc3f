#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# mypy: disable-error-code=var-annotated

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: November 13, 2024
"""

from io import BytesIO
import inspect
import queue
from time import sleep
from typing import Optional

from matplotlib import pyplot as plt
import matplotlib.gridspec as gridspec
import numpy as np
from matplotlib import get_backend, use

from pxl.util.decorators import ensure_fixed_interval
from pxl.plot.dt import DynamicTable
from pxl.plot.trace_diagram import TraceDiagram
from pxl.util.watermark import add_watermark2
from pxl.util.pusher import BytesIOPusher
from pxl.util.thread import running_in_main_thread


class GDMBCalculator:
    """
    A class to calculate GDMB values dynamically or statically from provided Y color coordinates.
    """

    def __init__(self, tmax: float, debug: bool = True, dynamic: bool = True, width: float = 15, height: float = 10,
                 scale_tolerance: float = 0.1, dy_receive: float = 0.01,
                 delta_tolerance: float = 0.1, measurement_step: int = 80, show_figure: bool = True,
                 bytesio_pusher: Optional[BytesIOPusher] = None, logo=None):

        """
        GDMB Calculator Parent Class

        Parameters:
            tmax (float): Max value of target data.
            debug (bool): If true, adds another subplot to show table with scaled measurement data.
            dynamic (bool): If true, run dynamic mode plotting.
            width (float): Width of plot.
            height (float): Height of plot.
            scale_tolerance (float): Threshold percentage for scaled luminance data (default=0.1).
            dy_receive (float): Value of dy that indicates max measurement data has been reached.
            delta_tolerance (float): Threshold percentage difference target and measurement data values (default=0.1).
            measurement_step (float): Step size (x-axis).

        """
        self.tmax = tmax
        self.mmax = .0
        self.debug = debug
        self._dynamic = dynamic
        self.scale_tolerance = scale_tolerance
        self.scale_target = ""
        self.scale_ratio = 0
        self.delta_tolerance = delta_tolerance
        self.measurement_step = measurement_step
        self._receive_flag = False
        self._max_flag = False
        self._flat_begin = 0
        self._max_counter = 0
        self.dy_receive = dy_receive
        self._m_arr = []  # type: ignore[var-annotated]
        self._show_figure = show_figure
        self._bytesio_fig_pusher = bytesio_pusher
        self._timer = None
        self._stop_draw = False
        print(f"Default backend: {get_backend()}")
        if (not self._show_figure) and self._dynamic:
            plt.close('all')
            backend = 'Agg'
            use(backend)
            print(f"Switched to backend \"{backend}\" for background drawing")
        self._fig = plt.figure(figsize=(width, height), dpi=100)
        self._anim = None
        self._data_queue = queue.Queue()
        self._x_points = []
        self._y_points = []
        self._t_max_arr = []
        self.upper_tar = []
        self.lower_tar = []
        self.scaled_data = None
        self._ax_data = None
        self._ax_params = None
        self._ax_trace = None
        self._ax_result = None
        self._dynam_params = None
        self._dynam_result = None

        plt.suptitle("GDMB Calculator", fontweight='bold')
        gs = gridspec.GridSpec(2, 2, figure=self._fig, width_ratios=[50, 45], height_ratios=[40, 40])
        if self.debug:
            self._ax_data = self._fig.add_subplot(gs[0, 0])
            self._ax_result = self._fig.add_subplot(gs[1, 0])
            self._dynam_result = DynamicTable(ax=self._ax_result,
                                              columns=["Lower Target Data", "Scaled Measurement Data",
                                                       "Upper Target Data"])
        else:
            self._ax_data = self._fig.add_subplot(gs[:, 0])

        self._ax_params = self._fig.add_subplot(gs[1, 1])
        self._ax_trace = self._fig.add_subplot(gs[0, 1])
        self._dynam_params = DynamicTable(ax=self._ax_params, columns=['Parameter', 'Value'],
                                          row_height=0.06, font_size=12)
        delta_row = ["Delta Tolerance", str(self.delta_tolerance * 100) + "%"]
        step_row = ["Measurement Step", str(self.measurement_step)]
        self._dynam_params.add_external_data(delta_row)
        self._dynam_params.add_external_data(step_row)
        self._trace_diagram = TraceDiagram(ax=self._ax_trace, tmax=self.tmax)
        self._trace_diagram.figure_assemble()
        self._dynam_data = DynamicTable(ax=self._ax_data,
                                        columns=["Pattern Index", "Target Data",
                                                 "Unscaled Measurement Data"],
                                        row_height=0.06, font_size=24)
        if logo:
            add_watermark2(self._fig, logo, size=(0.1, 0.1), alpha=0.02)

    def dynam_params_setup(self):
        """
        Setups up the Parameters Dynamic Table.
        """
        delta_row = ["Delta Tolerance", str(self.delta_tolerance * 100) + "%"]
        step_row = ["Measurement Step", str(self.measurement_step)]
        self._dynam_params.add_external_data(delta_row)
        self._dynam_params.add_external_data(step_row)

    def result_table_fill(self):
        """
        Adds scaled and target threshold data points to a new subplot.
        """
        color = "green"
        for i in range(len(self.scaled_data)):
            row = []
            row.append(self.lower_tar[i])
            row.append(self.scaled_data[i])
            row.append(self.upper_tar[i])
            if self.debug is True:
                self._dynam_result.add_external_data(row)
            if self.scaled_data[i] > self.upper_tar[i] or self.scaled_data[i] < self.lower_tar[i]:
                color = "red"
        return color

    def scale_factor(self):
        """
        Calculates the scale factor and applies to measurement data.
        """
        cond = abs(self.mmax - self.tmax) / self.mmax
        if cond <= self.scale_tolerance:
            self.scale_target = "Y"
            self.scale_ratio = self.mmax / self.tmax
            self.scaled_data = np.asarray(self._y_points) / self.scale_ratio
        else:
            self.scale_target = "N"
            self.scaled_data = np.copy(self._y_points)
        self.scale_params()

    def scale_params(self):
        """
        Add scale factor information to the parameter table.
        """
        scale_target_row = ["Scale Target (Y or N)", self.scale_target]
        scale_ratio_row = ["Scale Ratio", str(round(self.scale_ratio, 2)) + "%"]
        scale_tolerance_row = ["Scale Tolerance", str(self.scale_tolerance * 100) + "%"]
        self._dynam_params.add_external_data(scale_target_row)
        self._dynam_params.add_external_data(scale_ratio_row)
        self._dynam_params.add_external_data(scale_tolerance_row)

    def batch_receive_data(self, target_y_list: np.ndarray, measurement_y_list: np.ndarray):
        """
        Receives lists of target and measurement Y values, generates data point indices,
        and queues them for processing.

        Parameters:
        - target_y_list (np.ndarray): Target Y array.
        - measurement_Y_list (np.ndarray): Measurement Y array.

        """
        self.mmax = np.max(measurement_y_list)
        for i in range(measurement_y_list.size):
            pattern_index = i
            target_Y = target_y_list[i * 4]
            measurement_Y = measurement_y_list[i]
            if len(self._m_arr) > 1:
                self.max_measurement_detect(measurement_y=measurement_Y)
            if not self._max_flag:
                self._m_arr.append(measurement_Y)
                self._x_points.append(pattern_index * self.measurement_step)
                self._y_points.append(measurement_Y)
                self.upper_tar.append(target_Y + (self.delta_tolerance * 100))
                self.lower_tar.append(target_Y - (self.delta_tolerance * 100))
                self._trace_diagram.trace_plot(md_sections=np.asarray(self._x_points),
                                               measurement_data=np.asarray(self._y_points))
                row = [pattern_index, target_Y, measurement_Y]
                self._dynam_data.add_external_data(new_data=row)
        self.scale_factor()
        result_color = self.result_table_fill()
        self._trace_diagram.final_plot(md_sections=np.asarray(self._x_points),
                                       measurement_data=np.asarray(self._y_points),
                                       result_color=result_color,
                                       upper_tar=np.asarray(self.upper_tar),
                                       lower_tar=np.asarray(self.lower_tar))

    @ensure_fixed_interval(2.5)
    def receive_data(self, pattern_index: int, target_y: float, measurement_y: float,
                     redraw: bool = True, interval: float = 2.5) -> bool:
        """
        Execute a simulated meter measurement and ensure the total execution time
        meets a specified interval.

        Parameters:
        - step (int): DM Metadata Section (as seen on measurement).
        - target_y (float): Target Y.
        - measurement_y (float): Measurement Y.
        - redraw (bool, optional): Whether to redraw the plot.
        - interval (float, optional): The total time that the function call should take. If not specified,
          a default of 1 second is used. The value cannot be lower than 1 second to ensure rendering time.

        Raises:
        - ValueError: If target_Y is and measured_Y are not floats.

        Returns:
        - True if max measurement value is detected (_max_flag = True).
        - False otherwise.
        """
        if not isinstance(target_y, float) or not isinstance(measurement_y, float):
            raise ValueError('Target_Y and measured_Y values must be floats.')
        if measurement_y > self.mmax:
            self.mmax = measurement_y
        if len(self._m_arr) > 1:
            self.max_measurement_detect(measurement_y=measurement_y)
        self._m_arr.append(measurement_y)
        sleep(0.5)
        if not self._max_flag:
            self._data_queue.put((pattern_index, target_y, measurement_y, redraw))
        return self._max_flag

    def max_measurement_detect(self, measurement_y: float, begin_count: int = 10, end_count: int = 5):
        """
        Checks measurement data values and determines if max has been reached.

        Parameters:
        - measurement_y (float): Measurement Y.
        """
        prev_y = self._m_arr[len(self._m_arr) - 1]
        dy = abs(measurement_y - prev_y) / measurement_y
        if self._receive_flag is False:
            if dy > self.dy_receive:
                self._receive_flag = True
            else:
                self._flat_begin += 1
                if self._flat_begin > begin_count:
                    self._max_flag = True
        elif self._receive_flag:
            if dy < self.dy_receive:
                self._max_counter += 1
            if self._max_counter > end_count:
                self._max_flag = True

    def done(self):
        """
        Send a signal to exit the drawing process and close the plot.
        """
        self.scale_factor()
        result_color: str = self.result_table_fill()
        self._trace_diagram.final_plot(md_sections=np.asarray(self._x_points),
                                       measurement_data=np.asarray(self.scaled_data),
                                       result_color=result_color,
                                       upper_tar=np.asarray(self.upper_tar),
                                       lower_tar=np.asarray(self.lower_tar))
        self._fig.canvas.draw_idle()
        self._fig.canvas.flush_events()
        if not self._dynamic:
            return
        print("Closing GDMB Calculator")
        self._stop_draw = True
        sleep(3)
        plt.close(self._fig)

    def plot_figure(self, save_figure=None):
        """
        Function to control the plotting behavior.

        This function must be called from the main thread.

        Parameters:
        - save_figure (str, optional): Path to save the figure if specified.

        """
        if self._show_figure and self._dynamic:
            running_in_main_thread(inspect.currentframe().f_code.co_name)

        if self._dynamic:
            self._timer = self._fig.canvas.new_timer(interval=5000)

            def timer_callback():
                self._update_plot(None)
                if not self._show_figure and self._bytesio_fig_pusher is not None:
                    fig_buffer: BytesIO = BytesIO()
                    self._fig.savefig(fig_buffer, format='png')
                    fig_buffer.seek(0)
                    self._bytesio_fig_pusher.push(fig_buffer)
                    fig_buffer.close()
                if self._stop_draw:
                    self._timer.stop()

            self._timer.add_callback(timer_callback)
            self._timer.start()
        else:
            while not self._data_queue.empty():
                self._update_plot(None)

        plt.tight_layout()
        if self._show_figure:
            plt.show()
        elif self._dynamic:
            while not self._stop_draw:
                # manually trigger Timer callback (ugly, but the only option found working with Agg backend so far)
                for callback, args, kwargs in self._timer.callbacks:
                    callback(*args, **kwargs)
                sleep(0.5)

        if save_figure:
            self._fig.savefig(save_figure, dpi=300)

    def _init_plot(self):
        return ()

    def _update_plot(self, frame):
        data = self._update_data()
        if data and not self._max_flag:
            pattern_index, target_Y, measurement_Y, redraw = data
            self._x_points.append(pattern_index * self.measurement_step)
            self._y_points.append(measurement_Y)
            self.upper_tar.append(target_Y + (self.delta_tolerance * 100))
            self.lower_tar.append(target_Y - (self.delta_tolerance * 100))
            self._trace_diagram.trace_plot(md_sections=np.asarray(self._x_points),
                                           measurement_data=np.asarray(self._y_points))
            row = [pattern_index] + [target_Y] + [measurement_Y]
            self._dynam_data.add_external_data(new_data=row)
            self._fig.canvas.draw_idle()
            self._fig.canvas.flush_events()

    def _update_data(self):
        try:
            pattern_index, target_Y, measured_Y, redraw = self._data_queue.get_nowait()
            return pattern_index, target_Y, measured_Y, redraw
        except queue.Empty:
            return None
