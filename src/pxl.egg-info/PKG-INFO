Metadata-Version: 2.4
Name: pxl
Version: 0.2.4
Author: <PERSON>, <PERSON>
Author-email: <EMAIL>, <EMAIL>
Keywords: PXL,Dolby
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Dolby Licensees
Classifier: License :: Dolby Laboratories :: Proprietary
Classifier: Programming Language :: Python :: 3.11
Classifier: Operating System :: OS Independent
Classifier: Topic :: Utilities
Requires-Python: >=3.11, <4
Requires-Dist: numpy<2,>=1.26.2
Requires-Dist: scipy<2,>=1.12.0
Requires-Dist: matplotlib<3.9,>=3.8.4
Requires-Dist: openpyxl<4,>=3.1.2
Requires-Dist: pandas<3,>=2.2.2
Provides-Extra: build
Requires-Dist: build>=1.2.1; extra == "build"
Requires-Dist: setuptools>=68.2.0; extra == "build"
Requires-Dist: wheel>=0.41.2; extra == "build"
Provides-Extra: test
Requires-Dist: parameterized>=0.9.0; extra == "test"
Requires-Dist: flask; extra == "test"
Requires-Dist: requests; extra == "test"
Requires-Dist: coverage; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: keywords
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python

# pxl

```
cd existing_repo
git remote add origin https://gitlab-sfo.dolby.net/dv/tools/pxl.git
git branch -M master
git push -uf origin master
```

## Name
pxl

## Description
A tool suite for pixel analysis and evaluation.

## Authors and acknowledgment
Ethan Li (<EMAIL>)

## License
Dolby Laboratories.

## Project status
Beta
