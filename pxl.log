2025-07-07 21:36:55,740 - pxl.util.utils - ERROR - raise_without_traceback:40 - ValueError: Test error message
2025-07-07 21:37:24,670 - __main__ - ERROR - wrapper:49 - Error in test_function: Test error
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/util/error_handling.py", line 45, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 19, in test_function
ValueError: Test error
2025-07-07 21:50:52,830 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:51:29,760 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:52:10,105 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:53:19,492 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:53:19,505 - pxl.de_calc.de_calc_v2 - INFO - __init__:103 - Default backend: MacOSX
2025-07-07 21:53:19,615 - pxl.de_calc.de_calc_v2 - INFO - set_reference_white:187 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:14:16,653 - pxl.web.api - INFO - log_request:228 - Request: GET /api/info from 127.0.0.1 by anonymous
2025-07-07 22:14:16,653 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /api/info by anonymous
2025-07-07 22:14:16,655 - pxl.web.api - INFO - log_request:228 - Request: GET /health from 127.0.0.1 by anonymous
2025-07-07 22:14:16,655 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /health by anonymous
2025-07-07 22:14:16,656 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:16,656 - pxl.web.auth - WARNING - decorated_function:148 - Unauthenticated request to calculate_delta_e from 127.0.0.1
2025-07-07 22:14:16,657 - pxl.web.validation - ERROR - decorated_function:236 - Unexpected error in validation: Authentication required
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/validation.py", line 229, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/auth.py", line 149, in decorated_function
    raise AuthenticationError("Authentication required")
pxl.web.auth.AuthenticationError: Authentication required
2025-07-07 22:14:16,657 - pxl.web.api - INFO - log_response:234 - Response: 500 for POST /api/de/calculate by anonymous
2025-07-07 22:14:16,659 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:16,659 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: limited_user
2025-07-07 22:14:17,790 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user limited_user
2025-07-07 22:14:17,792 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by limited_user
2025-07-07 22:14:17,798 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:17,798 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:18,834 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:18,835 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:18,842 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,842 - pxl.web.auth - WARNING - decorated_function:148 - Unauthenticated request to calculate_delta_e from 127.0.0.1
2025-07-07 22:14:18,842 - pxl.web.validation - ERROR - decorated_function:236 - Unexpected error in validation: Authentication required
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/validation.py", line 229, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/auth.py", line 149, in decorated_function
    raise AuthenticationError("Authentication required")
pxl.web.auth.AuthenticationError: Authentication required
2025-07-07 22:14:18,843 - pxl.web.api - INFO - log_response:234 - Response: 500 for POST /api/de/calculate by anonymous
2025-07-07 22:14:18,847 - pxl.web.api - INFO - log_request:228 - Request: POST /api/gdmb/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,847 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: limited_user
2025-07-07 22:14:18,847 - pxl.web.auth - WARNING - decorated_function:153 - Unauthorized request to calculate_gdmb by limited_user
2025-07-07 22:14:18,847 - pxl.web.validation - ERROR - decorated_function:236 - Unexpected error in validation: Insufficient permissions
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/validation.py", line 229, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/auth.py", line 154, in decorated_function
    raise AuthorizationError("Insufficient permissions")
pxl.web.auth.AuthorizationError: Insufficient permissions
2025-07-07 22:14:18,848 - pxl.web.api - INFO - log_response:234 - Response: 500 for POST /api/gdmb/calculate by anonymous
2025-07-07 22:14:18,851 - pxl.web.api - INFO - log_request:228 - Request: GET /health from 127.0.0.1 by anonymous
2025-07-07 22:14:18,852 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /health by anonymous
2025-07-07 22:14:18,854 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,854 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:18,855 - pxl.web.validation - WARNING - decorated_function:232 - Validation error from 127.0.0.1: Missing required field: target_xyz
2025-07-07 22:14:18,855 - pxl.web.api - INFO - log_response:234 - Response: 400 for POST /api/de/calculate by test_user
2025-07-07 22:14:18,855 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,855 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:18,855 - pxl.web.validation - WARNING - decorated_function:232 - Validation error from 127.0.0.1: Invalid value for field 'target_xyz': XYZ array must have exactly 3 elements
2025-07-07 22:14:18,855 - pxl.web.api - INFO - log_response:234 - Response: 400 for POST /api/de/calculate by test_user
2025-07-07 22:14:18,856 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,856 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:18,856 - pxl.web.validation - WARNING - decorated_function:232 - Validation error from 127.0.0.1: Invalid value for field 'max_de_threshold': Delta E threshold must have exactly 3 elements
2025-07-07 22:14:18,856 - pxl.web.api - INFO - log_response:234 - Response: 400 for POST /api/de/calculate by test_user
2025-07-07 22:14:18,858 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,858 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:19,897 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:19,897 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:19,898 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:19,898 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:20,965 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:20,966 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:20,967 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:20,967 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:22,009 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:22,010 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:22,010 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:22,010 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:23,041 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:23,042 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:23,043 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:23,043 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:24,087 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:24,088 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:24,089 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:24,089 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:25,128 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:25,129 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:25,130 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:25,130 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:26,171 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:26,172 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:26,173 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:26,173 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:27,213 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:27,214 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:27,215 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:27,215 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:28,257 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:28,258 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:28,259 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:28,259 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:29,371 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:29,371 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:29,372 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:29,372 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:30,414 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:30,415 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:30,421 - pxl.web.api - INFO - log_request:228 - Request: GET /health from 127.0.0.1 by anonymous
2025-07-07 22:14:30,422 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /health by anonymous
2025-07-07 22:15:19,650 - pxl.web.api - INFO - log_request:228 - Request: GET /health from 127.0.0.1 by anonymous
2025-07-07 22:15:19,651 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /health by anonymous
2025-07-07 22:15:19,651 - pxl.web.api - INFO - log_request:228 - Request: GET /api/info from 127.0.0.1 by anonymous
2025-07-07 22:15:19,651 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /api/info by anonymous
2025-07-07 22:15:19,651 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:15:19,651 - pxl.web.auth - WARNING - decorated_function:148 - Unauthenticated request to calculate_delta_e from 127.0.0.1
2025-07-07 22:15:19,651 - pxl.web.auth - WARNING - decorated_function:184 - Authentication error from 127.0.0.1: Authentication required
2025-07-07 22:15:19,652 - pxl.web.api - INFO - log_response:234 - Response: 401 for POST /api/de/calculate by anonymous
2025-07-07 22:15:19,652 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:15:19,652 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:15:20,760 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:15:20,761 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:16:09,299 - pxl.web.server - WARNING - __init__:63 - Running in development mode with relaxed security!
