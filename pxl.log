2025-07-07 21:36:55,740 - pxl.util.utils - ERROR - raise_without_traceback:40 - ValueError: Test error message
2025-07-07 21:37:24,670 - __main__ - ERROR - wrapper:49 - Error in test_function: Test error
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/util/error_handling.py", line 45, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 19, in test_function
ValueError: Test error
2025-07-07 21:50:52,830 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:51:29,760 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:52:10,105 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:53:19,492 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:53:19,505 - pxl.de_calc.de_calc_v2 - INFO - __init__:103 - Default backend: MacOSX
2025-07-07 21:53:19,615 - pxl.de_calc.de_calc_v2 - INFO - set_reference_white:187 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:14:16,653 - pxl.web.api - INFO - log_request:228 - Request: GET /api/info from 127.0.0.1 by anonymous
2025-07-07 22:14:16,653 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /api/info by anonymous
2025-07-07 22:14:16,655 - pxl.web.api - INFO - log_request:228 - Request: GET /health from 127.0.0.1 by anonymous
2025-07-07 22:14:16,655 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /health by anonymous
2025-07-07 22:14:16,656 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:16,656 - pxl.web.auth - WARNING - decorated_function:148 - Unauthenticated request to calculate_delta_e from 127.0.0.1
2025-07-07 22:14:16,657 - pxl.web.validation - ERROR - decorated_function:236 - Unexpected error in validation: Authentication required
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/validation.py", line 229, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/auth.py", line 149, in decorated_function
    raise AuthenticationError("Authentication required")
pxl.web.auth.AuthenticationError: Authentication required
2025-07-07 22:14:16,657 - pxl.web.api - INFO - log_response:234 - Response: 500 for POST /api/de/calculate by anonymous
2025-07-07 22:14:16,659 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:16,659 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: limited_user
2025-07-07 22:14:17,790 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user limited_user
2025-07-07 22:14:17,792 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by limited_user
2025-07-07 22:14:17,798 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:17,798 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:18,834 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:18,835 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:18,842 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,842 - pxl.web.auth - WARNING - decorated_function:148 - Unauthenticated request to calculate_delta_e from 127.0.0.1
2025-07-07 22:14:18,842 - pxl.web.validation - ERROR - decorated_function:236 - Unexpected error in validation: Authentication required
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/validation.py", line 229, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/auth.py", line 149, in decorated_function
    raise AuthenticationError("Authentication required")
pxl.web.auth.AuthenticationError: Authentication required
2025-07-07 22:14:18,843 - pxl.web.api - INFO - log_response:234 - Response: 500 for POST /api/de/calculate by anonymous
2025-07-07 22:14:18,847 - pxl.web.api - INFO - log_request:228 - Request: POST /api/gdmb/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,847 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: limited_user
2025-07-07 22:14:18,847 - pxl.web.auth - WARNING - decorated_function:153 - Unauthorized request to calculate_gdmb by limited_user
2025-07-07 22:14:18,847 - pxl.web.validation - ERROR - decorated_function:236 - Unexpected error in validation: Insufficient permissions
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/validation.py", line 229, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/web/auth.py", line 154, in decorated_function
    raise AuthorizationError("Insufficient permissions")
pxl.web.auth.AuthorizationError: Insufficient permissions
2025-07-07 22:14:18,848 - pxl.web.api - INFO - log_response:234 - Response: 500 for POST /api/gdmb/calculate by anonymous
2025-07-07 22:14:18,851 - pxl.web.api - INFO - log_request:228 - Request: GET /health from 127.0.0.1 by anonymous
2025-07-07 22:14:18,852 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /health by anonymous
2025-07-07 22:14:18,854 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,854 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:18,855 - pxl.web.validation - WARNING - decorated_function:232 - Validation error from 127.0.0.1: Missing required field: target_xyz
2025-07-07 22:14:18,855 - pxl.web.api - INFO - log_response:234 - Response: 400 for POST /api/de/calculate by test_user
2025-07-07 22:14:18,855 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,855 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:18,855 - pxl.web.validation - WARNING - decorated_function:232 - Validation error from 127.0.0.1: Invalid value for field 'target_xyz': XYZ array must have exactly 3 elements
2025-07-07 22:14:18,855 - pxl.web.api - INFO - log_response:234 - Response: 400 for POST /api/de/calculate by test_user
2025-07-07 22:14:18,856 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,856 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:18,856 - pxl.web.validation - WARNING - decorated_function:232 - Validation error from 127.0.0.1: Invalid value for field 'max_de_threshold': Delta E threshold must have exactly 3 elements
2025-07-07 22:14:18,856 - pxl.web.api - INFO - log_response:234 - Response: 400 for POST /api/de/calculate by test_user
2025-07-07 22:14:18,858 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:18,858 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:19,897 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:19,897 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:19,898 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:19,898 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:20,965 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:20,966 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:20,967 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:20,967 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:22,009 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:22,010 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:22,010 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:22,010 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:23,041 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:23,042 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:23,043 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:23,043 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:24,087 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:24,088 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:24,089 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:24,089 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:25,128 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:25,129 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:25,130 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:25,130 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:26,171 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:26,172 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:26,173 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:26,173 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:27,213 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:27,214 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:27,215 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:27,215 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:28,257 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:28,258 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:28,259 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:28,259 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:29,371 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:29,371 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:29,372 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:14:29,372 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:14:30,414 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:14:30,415 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:14:30,421 - pxl.web.api - INFO - log_request:228 - Request: GET /health from 127.0.0.1 by anonymous
2025-07-07 22:14:30,422 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /health by anonymous
2025-07-07 22:15:19,650 - pxl.web.api - INFO - log_request:228 - Request: GET /health from 127.0.0.1 by anonymous
2025-07-07 22:15:19,651 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /health by anonymous
2025-07-07 22:15:19,651 - pxl.web.api - INFO - log_request:228 - Request: GET /api/info from 127.0.0.1 by anonymous
2025-07-07 22:15:19,651 - pxl.web.api - INFO - log_response:234 - Response: 200 for GET /api/info by anonymous
2025-07-07 22:15:19,651 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:15:19,651 - pxl.web.auth - WARNING - decorated_function:148 - Unauthenticated request to calculate_delta_e from 127.0.0.1
2025-07-07 22:15:19,651 - pxl.web.auth - WARNING - decorated_function:184 - Authentication error from 127.0.0.1: Authentication required
2025-07-07 22:15:19,652 - pxl.web.api - INFO - log_response:234 - Response: 401 for POST /api/de/calculate by anonymous
2025-07-07 22:15:19,652 - pxl.web.api - INFO - log_request:228 - Request: POST /api/de/calculate from 127.0.0.1 by anonymous
2025-07-07 22:15:19,652 - pxl.web.auth - INFO - authenticate:106 - Authenticated user: test_user
2025-07-07 22:15:20,760 - pxl.web.api - INFO - calculate_delta_e:140 - Delta E calculation completed for user test_user
2025-07-07 22:15:20,761 - pxl.web.api - INFO - log_response:234 - Response: 200 for POST /api/de/calculate by test_user
2025-07-07 22:16:09,299 - pxl.web.server - WARNING - __init__:63 - Running in development mode with relaxed security!
2025-07-07 22:30:28,558 - DeltaECalculatorRefactored - INFO - __init__:66 - Initialized DeltaECalculatorRefactored
2025-07-07 22:30:28,567 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:30:28,568 - DeltaECalculatorRefactored - INFO - __init__:119 - DeltaE Calculator (Refactored) initialized
2025-07-07 22:30:28,568 - DeltaECalculatorRefactored - INFO - initialize:123 - Initializing DeltaE Calculator components...
2025-07-07 22:30:28,698 - DeltaECalculatorRefactored - INFO - initialize:142 - DeltaE Calculator initialization completed
2025-07-07 22:30:28,698 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:30:28,698 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:30:28,698 - DeltaECalculatorRefactored - INFO - set_reference_white:221 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:30:29,700 - DeltaECalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
2025-07-07 22:31:47,335 - pxl.de_calc.de_calc - INFO - create_refactored:76 - Creating refactored DeltaECalculator with improved separation of concerns
2025-07-07 22:33:12,248 - pxl.de_calc.de_calc - INFO - create_refactored:76 - Creating refactored DeltaECalculator with improved separation of concerns
2025-07-07 22:33:12,249 - DeltaECalculatorRefactored - INFO - __init__:66 - Initialized DeltaECalculatorRefactored
2025-07-07 22:33:12,260 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:33:12,260 - DeltaECalculatorRefactored - INFO - __init__:119 - DeltaE Calculator (Refactored) initialized
2025-07-07 22:33:12,260 - DeltaECalculatorRefactored - INFO - initialize:123 - Initializing DeltaE Calculator components...
2025-07-07 22:33:12,380 - DeltaECalculatorRefactored - INFO - initialize:142 - DeltaE Calculator initialization completed
2025-07-07 22:33:12,380 - pxl.calc.compatibility - INFO - __init__:103 - DeltaE Calculator Adapter initialized
2025-07-07 22:33:12,380 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:33:12,380 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:33:12,380 - DeltaECalculatorRefactored - INFO - set_reference_white:221 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:33:13,382 - DeltaECalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
2025-07-07 22:35:28,319 - DeltaECalculatorRefactored - INFO - __init__:66 - Initialized DeltaECalculatorRefactored
2025-07-07 22:35:28,327 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:35:28,327 - DeltaECalculatorRefactored - INFO - __init__:119 - DeltaE Calculator (Refactored) initialized
2025-07-07 22:35:28,328 - DeltaECalculatorRefactored - INFO - initialize:123 - Initializing DeltaE Calculator components...
2025-07-07 22:35:28,434 - DeltaECalculatorRefactored - INFO - initialize:142 - DeltaE Calculator initialization completed
2025-07-07 22:35:28,434 - pxl.calc.compatibility - INFO - __init__:103 - DeltaE Calculator Adapter initialized
2025-07-07 22:35:28,435 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:28,435 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:28,435 - DeltaECalculatorRefactored - INFO - set_reference_white:221 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:29,436 - DeltaECalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
2025-07-07 22:35:29,440 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.3 0.3 0.3]
2025-07-07 22:35:29,480 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.3 0.3 0.3]
2025-07-07 22:35:29,500 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 22:35:29,501 - pxl.de_calc.de_calc_v2 - INFO - __init__:103 - Default backend: MacOSX
2025-07-07 22:35:29,538 - pxl.de_calc.de_calc - INFO - create_refactored:76 - Creating refactored DeltaECalculator with improved separation of concerns
2025-07-07 22:35:29,539 - DeltaECalculatorRefactored - INFO - __init__:66 - Initialized DeltaECalculatorRefactored
2025-07-07 22:35:29,539 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:35:29,539 - DeltaECalculatorRefactored - INFO - __init__:119 - DeltaE Calculator (Refactored) initialized
2025-07-07 22:35:29,539 - DeltaECalculatorRefactored - INFO - initialize:123 - Initializing DeltaE Calculator components...
2025-07-07 22:35:29,564 - DeltaECalculatorRefactored - INFO - initialize:142 - DeltaE Calculator initialization completed
2025-07-07 22:35:29,564 - pxl.calc.compatibility - INFO - __init__:103 - DeltaE Calculator Adapter initialized
2025-07-07 22:35:29,564 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:29,564 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:29,564 - DeltaECalculatorRefactored - INFO - set_reference_white:221 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:30,566 - DeltaECalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
2025-07-07 22:35:30,570 - GDMBComputation - INFO - calculate_scale_factor:306 - Scale factor calculated: 5.2632
2025-07-07 22:35:30,571 - DeltaECalculatorRefactored - INFO - __init__:66 - Initialized DeltaECalculatorRefactored
2025-07-07 22:35:30,571 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:35:30,571 - DeltaECalculatorRefactored - INFO - __init__:119 - DeltaE Calculator (Refactored) initialized
2025-07-07 22:35:30,571 - DeltaECalculatorRefactored - INFO - initialize:123 - Initializing DeltaE Calculator components...
2025-07-07 22:35:30,610 - DeltaECalculatorRefactored - INFO - initialize:142 - DeltaE Calculator initialization completed
2025-07-07 22:35:30,610 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:30,610 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:30,610 - DeltaECalculatorRefactored - INFO - set_reference_white:221 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:35:31,612 - DeltaECalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
2025-07-07 22:35:31,615 - GDMBCalculatorRefactored - INFO - __init__:66 - Initialized GDMBCalculatorRefactored
2025-07-07 22:35:31,615 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:35:31,615 - GDMBCalculatorRefactored - INFO - __init__:100 - GDMB Calculator (Refactored) initialized
2025-07-07 22:35:31,615 - GDMBCalculatorRefactored - INFO - initialize:104 - Initializing GDMB Calculator components...
2025-07-07 22:36:12,665 - DeltaECalculatorRefactored - INFO - __init__:66 - Initialized DeltaECalculatorRefactored
2025-07-07 22:36:12,676 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:36:12,676 - DeltaECalculatorRefactored - INFO - __init__:119 - DeltaE Calculator (Refactored) initialized
2025-07-07 22:36:12,676 - DeltaECalculatorRefactored - INFO - initialize:123 - Initializing DeltaE Calculator components...
2025-07-07 22:36:12,777 - DeltaECalculatorRefactored - INFO - initialize:142 - DeltaE Calculator initialization completed
2025-07-07 22:36:12,777 - pxl.calc.compatibility - INFO - __init__:103 - DeltaE Calculator Adapter initialized
2025-07-07 22:36:12,777 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:12,777 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:12,777 - DeltaECalculatorRefactored - INFO - set_reference_white:221 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:13,779 - DeltaECalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
2025-07-07 22:36:13,784 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.3 0.3 0.3]
2025-07-07 22:36:13,829 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.3 0.3 0.3]
2025-07-07 22:36:13,841 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 22:36:13,843 - pxl.de_calc.de_calc_v2 - INFO - __init__:103 - Default backend: MacOSX
2025-07-07 22:36:13,883 - pxl.de_calc.de_calc - INFO - create_refactored:76 - Creating refactored DeltaECalculator with improved separation of concerns
2025-07-07 22:36:13,883 - DeltaECalculatorRefactored - INFO - __init__:66 - Initialized DeltaECalculatorRefactored
2025-07-07 22:36:13,884 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:36:13,884 - DeltaECalculatorRefactored - INFO - __init__:119 - DeltaE Calculator (Refactored) initialized
2025-07-07 22:36:13,884 - DeltaECalculatorRefactored - INFO - initialize:123 - Initializing DeltaE Calculator components...
2025-07-07 22:36:13,910 - DeltaECalculatorRefactored - INFO - initialize:142 - DeltaE Calculator initialization completed
2025-07-07 22:36:13,910 - pxl.calc.compatibility - INFO - __init__:103 - DeltaE Calculator Adapter initialized
2025-07-07 22:36:13,910 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:13,910 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:13,910 - DeltaECalculatorRefactored - INFO - set_reference_white:221 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:14,911 - DeltaECalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
2025-07-07 22:36:14,913 - GDMBComputation - INFO - calculate_scale_factor:306 - Scale factor calculated: 5.2632
2025-07-07 22:36:14,914 - DeltaECalculatorRefactored - INFO - __init__:66 - Initialized DeltaECalculatorRefactored
2025-07-07 22:36:14,914 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:36:14,914 - DeltaECalculatorRefactored - INFO - __init__:119 - DeltaE Calculator (Refactored) initialized
2025-07-07 22:36:14,914 - DeltaECalculatorRefactored - INFO - initialize:123 - Initializing DeltaE Calculator components...
2025-07-07 22:36:14,948 - DeltaECalculatorRefactored - INFO - initialize:142 - DeltaE Calculator initialization completed
2025-07-07 22:36:14,949 - pxl.calc.data_manager - INFO - set_reference_white:136 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:14,949 - DeltaEComputation - INFO - set_reference_white:83 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:14,949 - DeltaECalculatorRefactored - INFO - set_reference_white:221 - Reference white set to: [0.31 0.29 0.32]
2025-07-07 22:36:15,950 - DeltaECalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
2025-07-07 22:36:15,953 - GDMBCalculatorRefactored - INFO - __init__:66 - Initialized GDMBCalculatorRefactored
2025-07-07 22:36:15,954 - pxl.calc.figure_manager - INFO - _setup_backend:60 - Default backend: MacOSX
2025-07-07 22:36:15,955 - GDMBCalculatorRefactored - INFO - __init__:100 - GDMB Calculator (Refactored) initialized
2025-07-07 22:36:15,955 - GDMBCalculatorRefactored - INFO - initialize:104 - Initializing GDMB Calculator components...
2025-07-07 22:36:15,984 - GDMBCalculatorRefactored - INFO - initialize:121 - GDMB Calculator initialization completed
2025-07-07 22:36:16,986 - GDMBComputation - INFO - calculate_scale_factor:306 - Scale factor calculated: 5.2632
2025-07-07 22:36:16,986 - GDMBCalculatorRefactored - INFO - cleanup:142 - Calculator cleanup completed
