2025-07-07 21:36:55,740 - pxl.util.utils - ERROR - raise_without_traceback:40 - ValueError: Test error message
2025-07-07 21:37:24,670 - __main__ - ERROR - wrapper:49 - Error in test_function: Test error
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/pxl-dlb/src/pxl/util/error_handling.py", line 45, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 19, in test_function
ValueError: Test error
2025-07-07 21:50:52,830 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:51:29,760 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:52:10,105 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:53:19,492 - pxl.de_calc.de_calc - INFO - create_v2:64 - Creating DeltaECalculatorV2 with improved architecture
2025-07-07 21:53:19,505 - pxl.de_calc.de_calc_v2 - INFO - __init__:103 - Default backend: MacOSX
2025-07-07 21:53:19,615 - pxl.de_calc.de_calc_v2 - INFO - set_reference_white:187 - Reference white set to: [0.31 0.29 0.32]
